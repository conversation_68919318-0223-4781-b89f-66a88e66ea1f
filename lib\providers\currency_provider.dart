import 'package:flutter/foundation.dart';
import '../models/currency.dart';
import '../database/database_helper.dart';

class CurrencyProvider with ChangeNotifier {
  final DatabaseHelper _databaseHelper = DatabaseHelper();

  List<Currency> _currencies = [];
  bool _isLoading = false;
  String? _error;

  List<Currency> get currencies => _currencies;
  bool get isLoading => _isLoading;
  String? get error => _error;

  Currency? get defaultCurrency {
    if (_currencies.isEmpty) return null;

    try {
      return _currencies.firstWhere((currency) => currency.isDefault);
    } catch (e) {
      return _currencies.first;
    }
  }

  bool get hasCurrencies => _currencies.isNotEmpty;

  Future<void> loadCurrencies() async {
    _setLoading(true);
    _setError(null);

    try {
      _currencies = await _databaseHelper.getAllCurrencies();
      notifyListeners();
    } catch (e) {
      _setError('خطأ في تحميل العملات: ${e.toString()}');
    } finally {
      _setLoading(false);
    }
  }

  Future<bool> addCurrency(Currency currency) async {
    _setLoading(true);
    _setError(null);

    try {
      // Check if currency code already exists
      if (_currencies.any(
        (c) => c.code.toLowerCase() == currency.code.toLowerCase(),
      )) {
        _setError('كود العملة موجود مسبقاً');
        return false;
      }

      // Check if currency name already exists
      if (_currencies.any(
        (c) => c.name.toLowerCase() == currency.name.toLowerCase(),
      )) {
        _setError('اسم العملة موجود مسبقاً');
        return false;
      }

      int id = await _databaseHelper.insertCurrency(currency);
      Currency newCurrency = currency.copyWith(id: id);

      _currencies.add(newCurrency);
      _sortCurrencies();
      notifyListeners();

      return true;
    } catch (e) {
      _setError('خطأ في إضافة العملة: ${e.toString()}');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  Future<bool> updateCurrency(Currency currency) async {
    _setLoading(true);
    _setError(null);

    try {
      // Check if currency code already exists (excluding current currency)
      if (_currencies.any(
        (c) =>
            c.id != currency.id &&
            c.code.toLowerCase() == currency.code.toLowerCase(),
      )) {
        _setError('كود العملة موجود مسبقاً');
        return false;
      }

      // Check if currency name already exists (excluding current currency)
      if (_currencies.any(
        (c) =>
            c.id != currency.id &&
            c.name.toLowerCase() == currency.name.toLowerCase(),
      )) {
        _setError('اسم العملة موجود مسبقاً');
        return false;
      }

      await _databaseHelper.updateCurrency(currency);

      int index = _currencies.indexWhere((c) => c.id == currency.id);
      if (index != -1) {
        _currencies[index] = currency;
        _sortCurrencies();
        notifyListeners();
      }

      return true;
    } catch (e) {
      _setError('خطأ في تحديث العملة: ${e.toString()}');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  Future<bool> deleteCurrency(int id) async {
    _setLoading(true);
    _setError(null);

    try {
      // Check if currency is default
      Currency? currency = _currencies.firstWhere((c) => c.id == id);
      if (currency.isDefault) {
        _setError('لا يمكن حذف العملة الافتراضية');
        return false;
      }

      int result = await _databaseHelper.deleteCurrency(id);

      if (result > 0) {
        _currencies.removeWhere((c) => c.id == id);
        notifyListeners();
        return true;
      } else {
        _setError('لا يمكن حذف العملة - قد تكون مستخدمة في حسابات');
        return false;
      }
    } catch (e) {
      _setError('خطأ في حذف العملة: ${e.toString()}');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  Currency? getCurrencyById(int id) {
    try {
      return _currencies.firstWhere((currency) => currency.id == id);
    } catch (e) {
      return null;
    }
  }

  Currency? getCurrencyByCode(String code) {
    try {
      return _currencies.firstWhere(
        (currency) => currency.code.toLowerCase() == code.toLowerCase(),
      );
    } catch (e) {
      return null;
    }
  }

  List<String> get existingCurrencyCodes {
    return _currencies.map((c) => c.code.toUpperCase()).toList();
  }

  List<String> get existingCurrencyNames {
    return _currencies.map((c) => c.name).toList();
  }

  void _sortCurrencies() {
    _currencies.sort((a, b) {
      // Default currencies first
      if (a.isDefault && !b.isDefault) return -1;
      if (!a.isDefault && b.isDefault) return 1;

      // Then sort by name
      return a.name.compareTo(b.name);
    });
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String? error) {
    _error = error;
    if (error != null) {
      notifyListeners();
    }
  }

  void clearError() {
    _error = null;
    notifyListeners();
  }
}
