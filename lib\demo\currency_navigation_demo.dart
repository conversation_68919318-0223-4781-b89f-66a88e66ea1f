import 'package:flutter/material.dart';
import '../models/currency.dart';
import '../models/category.dart' as model;
import '../models/account.dart';
import '../utils/constants.dart';
import '../widgets/account_card.dart';

/// Demo screen to showcase the new currency page navigation feature
class CurrencyNavigationDemo extends StatefulWidget {
  const CurrencyNavigationDemo({super.key});

  @override
  State<CurrencyNavigationDemo> createState() => _CurrencyNavigationDemoState();
}

class _CurrencyNavigationDemoState extends State<CurrencyNavigationDemo> {
  PageController? _pageController;
  int _currentPageIndex = 0;
  
  // Demo data
  final List<Currency> _demoCurrencies = [
    Currency(id: 1, name: 'ريال سعودي', symbol: 'ر.س', code: 'SAR', isDefault: true),
    Currency(id: 2, name: 'دولار أمريكي', symbol: '\$', code: 'USD'),
    Currency(id: 3, name: 'يورو', symbol: '€', code: 'EUR'),
  ];
  
  final model.Category _demoCategory = model.Category(
    id: 1,
    name: 'العائلة',
    description: 'حسابات العائلة',
    isDefault: false,
  );

  @override
  void initState() {
    super.initState();
    _pageController = PageController();
  }

  @override
  void dispose() {
    _pageController?.dispose();
    super.dispose();
  }

  List<Account> _getDemoAccountsForCurrency(Currency currency) {
    // Generate demo accounts for each currency
    switch (currency.code) {
      case 'SAR':
        return [
          Account(
            id: 1,
            name: 'أحمد محمد',
            phoneNumber: '**********',
            amount: 1500.0,
            currencyId: currency.id!,
            categoryId: _demoCategory.id!,
            date: DateTime.now().subtract(const Duration(days: 2)),
            debtType: DebtType.owedToMe,
          ),
          Account(
            id: 2,
            name: 'فاطمة علي',
            phoneNumber: '**********',
            amount: 800.0,
            currencyId: currency.id!,
            categoryId: _demoCategory.id!,
            date: DateTime.now().subtract(const Duration(days: 5)),
            debtType: DebtType.iOwe,
          ),
        ];
      case 'USD':
        return [
          Account(
            id: 3,
            name: 'سارة أحمد',
            phoneNumber: '**********',
            amount: 250.0,
            currencyId: currency.id!,
            categoryId: _demoCategory.id!,
            date: DateTime.now().subtract(const Duration(days: 1)),
            debtType: DebtType.owedToMe,
          ),
        ];
      case 'EUR':
        return [
          Account(
            id: 4,
            name: 'محمد خالد',
            phoneNumber: '**********',
            amount: 180.0,
            currencyId: currency.id!,
            categoryId: _demoCategory.id!,
            date: DateTime.now().subtract(const Duration(days: 3)),
            debtType: DebtType.iOwe,
          ),
          Account(
            id: 5,
            name: 'نورا سالم',
            phoneNumber: '**********',
            amount: 320.0,
            currencyId: currency.id!,
            categoryId: _demoCategory.id!,
            date: DateTime.now().subtract(const Duration(days: 4)),
            debtType: DebtType.owedToMe,
          ),
        ];
      default:
        return [];
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('عرض توضيحي - تنقل العملات'),
        backgroundColor: Theme.of(context).colorScheme.surface,
        elevation: 0,
      ),
      body: Column(
        children: [
          // Feature description
          Container(
            width: double.infinity,
            margin: const EdgeInsets.all(AppConstants.defaultPadding),
            padding: const EdgeInsets.all(AppConstants.defaultPadding),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.primaryContainer,
              borderRadius: BorderRadius.circular(AppConstants.defaultBorderRadius),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'الميزة الجديدة: تنقل العملات',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).colorScheme.onPrimaryContainer,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'يمكنك الآن التنقل بين العملات المختلفة داخل نفس الفئة باستخدام:\n'
                  '• السحب يميناً ويساراً\n'
                  '• النقر على أزرار العملات\n'
                  '• مؤشرات النقاط السفلية',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Theme.of(context).colorScheme.onPrimaryContainer,
                  ),
                ),
              ],
            ),
          ),
          
          // Currency indicators
          _buildCurrencyIndicators(),
          
          // PageView for currency pages
          Expanded(
            child: PageView.builder(
              controller: _pageController,
              onPageChanged: (index) {
                setState(() {
                  _currentPageIndex = index;
                });
              },
              itemCount: _demoCurrencies.length,
              itemBuilder: (context, index) {
                return _buildCurrencyPage(_demoCurrencies[index]);
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCurrencyIndicators() {
    return Container(
      padding: const EdgeInsets.symmetric(
        vertical: AppConstants.smallPadding,
        horizontal: AppConstants.defaultPadding,
      ),
      child: Column(
        children: [
          // Currency tabs
          SizedBox(
            height: 50,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: _demoCurrencies.length,
              itemBuilder: (context, index) {
                final currency = _demoCurrencies[index];
                final isSelected = index == _currentPageIndex;
                
                return GestureDetector(
                  onTap: () {
                    _pageController?.animateToPage(
                      index,
                      duration: const Duration(milliseconds: 300),
                      curve: Curves.easeInOut,
                    );
                  },
                  child: Container(
                    margin: const EdgeInsets.symmetric(horizontal: 4),
                    padding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 8,
                    ),
                    decoration: BoxDecoration(
                      color: isSelected
                          ? Theme.of(context).colorScheme.primary
                          : Theme.of(context).colorScheme.surface,
                      borderRadius: BorderRadius.circular(20),
                      border: Border.all(
                        color: isSelected
                            ? Theme.of(context).colorScheme.primary
                            : Theme.of(context).colorScheme.outline,
                      ),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          currency.symbol,
                          style: TextStyle(
                            color: isSelected
                                ? Theme.of(context).colorScheme.onPrimary
                                : Theme.of(context).colorScheme.onSurface,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(width: 4),
                        Text(
                          currency.name,
                          style: TextStyle(
                            color: isSelected
                                ? Theme.of(context).colorScheme.onPrimary
                                : Theme.of(context).colorScheme.onSurface,
                            fontSize: 12,
                          ),
                        ),
                      ],
                    ),
                  ),
                );
              },
            ),
          ),
          const SizedBox(height: 8),
          // Page dots
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: _demoCurrencies.asMap().entries.map((entry) {
              final index = entry.key;
              final isSelected = index == _currentPageIndex;
              
              return Container(
                margin: const EdgeInsets.symmetric(horizontal: 3),
                width: isSelected ? 12 : 8,
                height: 8,
                decoration: BoxDecoration(
                  color: isSelected
                      ? Theme.of(context).colorScheme.primary
                      : Theme.of(context).colorScheme.outline,
                  borderRadius: BorderRadius.circular(4),
                ),
              );
            }).toList(),
          ),
        ],
      ),
    );
  }

  Widget _buildCurrencyPage(Currency currency) {
    final accounts = _getDemoAccountsForCurrency(currency);
    final totalOwedToMe = accounts
        .where((account) => account.debtType == DebtType.owedToMe)
        .fold(0.0, (sum, account) => sum + account.amount);
    final totalIOwe = accounts
        .where((account) => account.debtType == DebtType.iOwe)
        .fold(0.0, (sum, account) => sum + account.amount);

    return CustomScrollView(
      slivers: [
        // Summary Card
        SliverToBoxAdapter(
          child: AccountSummaryCard(
            title: '${currency.name} (${currency.symbol})',
            totalOwedToMe: totalOwedToMe,
            totalIOwe: totalIOwe,
            currency: currency,
          ),
        ),
        
        // Accounts List
        if (accounts.isEmpty)
          SliverFillRemaining(
            child: Center(
              child: Text(
                'لا توجد حسابات بعملة ${currency.name}',
                style: Theme.of(context).textTheme.titleMedium,
              ),
            ),
          )
        else
          SliverPadding(
            padding: const EdgeInsets.all(AppConstants.defaultPadding),
            sliver: SliverList(
              delegate: SliverChildBuilderDelegate(
                (context, index) {
                  final account = accounts[index];
                  return Padding(
                    padding: const EdgeInsets.only(
                      bottom: AppConstants.smallPadding,
                    ),
                    child: AccountCard(
                      account: account,
                      currency: currency,
                      showActions: false, // Hide edit/delete for demo
                    ),
                  );
                },
                childCount: accounts.length,
              ),
            ),
          ),
      ],
    );
  }
}
