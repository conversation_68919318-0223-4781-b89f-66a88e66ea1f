enum DebtType {
  owedToMe, // له
  iOwe, // عليه
}

class Account {
  final int? id;
  final String name;
  final String phoneNumber;
  final double amount;
  final String? details;
  final int currencyId;
  final int categoryId;
  final DateTime date;
  final DebtType debtType;
  final DateTime createdAt;
  final DateTime? updatedAt;

  Account({
    this.id,
    required this.name,
    required this.phoneNumber,
    required this.amount,
    this.details,
    required this.currencyId,
    required this.categoryId,
    required this.date,
    required this.debtType,
    DateTime? createdAt,
    this.updatedAt,
  }) : createdAt = createdAt ?? DateTime.now();

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'phoneNumber': phoneNumber,
      'amount': amount,
      'details': details,
      'currencyId': currencyId,
      'categoryId': categoryId,
      'date': date.millisecondsSinceEpoch,
      'debtType': debtType.index,
      'createdAt': createdAt.millisecondsSinceEpoch,
      'updatedAt': updatedAt?.millisecondsSinceEpoch,
    };
  }

  factory Account.fromMap(Map<String, dynamic> map) {
    return Account(
      id: map['id'],
      name: map['name'],
      phoneNumber: map['phoneNumber'],
      amount: map['amount'].toDouble(),
      details: map['details'],
      currencyId: map['currencyId'],
      categoryId: map['categoryId'],
      date: DateTime.fromMillisecondsSinceEpoch(map['date']),
      debtType: DebtType.values[map['debtType']],
      createdAt: DateTime.fromMillisecondsSinceEpoch(map['createdAt']),
      updatedAt: map['updatedAt'] != null
          ? DateTime.fromMillisecondsSinceEpoch(map['updatedAt'])
          : null,
    );
  }

  Account copyWith({
    int? id,
    String? name,
    String? phoneNumber,
    double? amount,
    String? details,
    int? currencyId,
    int? categoryId,
    DateTime? date,
    DebtType? debtType,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Account(
      id: id ?? this.id,
      name: name ?? this.name,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      amount: amount ?? this.amount,
      details: details ?? this.details,
      currencyId: currencyId ?? this.currencyId,
      categoryId: categoryId ?? this.categoryId,
      date: date ?? this.date,
      debtType: debtType ?? this.debtType,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? DateTime.now(),
    );
  }

  String get debtTypeArabic {
    switch (debtType) {
      case DebtType.owedToMe:
        return 'له';
      case DebtType.iOwe:
        return 'عليه';
    }
  }

  @override
  String toString() {
    return 'Account{id: $id, name: $name, phoneNumber: $phoneNumber, amount: $amount, debtType: $debtType}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Account &&
        other.id == id &&
        other.name == name &&
        other.phoneNumber == phoneNumber &&
        other.amount == amount &&
        other.details == details &&
        other.currencyId == currencyId &&
        other.categoryId == categoryId &&
        other.date == date &&
        other.debtType == debtType;
  }

  @override
  int get hashCode {
    return id.hashCode ^
        name.hashCode ^
        phoneNumber.hashCode ^
        amount.hashCode ^
        details.hashCode ^
        currencyId.hashCode ^
        categoryId.hashCode ^
        date.hashCode ^
        debtType.hashCode;
  }
}
