import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/account.dart';
import '../models/currency.dart';
import '../models/category.dart' as model;
import '../providers/account_provider.dart';
import '../providers/currency_provider.dart';
import '../providers/category_provider.dart';
import '../widgets/custom_text_field.dart';
import '../widgets/debt_type_selector.dart';
import '../utils/constants.dart';
import '../utils/validators.dart';
import '../utils/formatters.dart';

class SimpleAddAccountModal {
  static Future<bool?> show(BuildContext context, {Account? accountToEdit}) {
    return showModalBottomSheet<bool>(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      enableDrag: true,
      isDismissible: true,
      builder:
          (context) =>
              _SimpleAddAccountModalContent(accountToEdit: accountToEdit),
    );
  }
}

class _SimpleAddAccountModalContent extends StatefulWidget {
  final Account? accountToEdit;

  const _SimpleAddAccountModalContent({this.accountToEdit});

  @override
  State<_SimpleAddAccountModalContent> createState() =>
      _SimpleAddAccountModalContentState();
}

class _SimpleAddAccountModalContentState
    extends State<_SimpleAddAccountModalContent> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _phoneController = TextEditingController();
  final _amountController = TextEditingController();
  final _detailsController = TextEditingController();

  Currency? _selectedCurrency;
  model.Category? _selectedCategory;
  DebtType? _selectedDebtType;
  DateTime _selectedDate = DateTime.now();
  bool _isLoading = false;

  bool get isEditing => widget.accountToEdit != null;

  @override
  void initState() {
    super.initState();
    _initializeForm();
  }

  void _initializeForm() {
    if (isEditing) {
      final account = widget.accountToEdit!;
      _nameController.text = account.name;
      _phoneController.text = account.phoneNumber;
      _amountController.text = account.amount.toString();
      _detailsController.text = account.details ?? '';
      _selectedDebtType = account.debtType;
      _selectedDate = account.date;

      WidgetsBinding.instance.addPostFrameCallback((_) {
        final currencyProvider = context.read<CurrencyProvider>();
        final categoryProvider = context.read<CategoryProvider>();

        setState(() {
          _selectedCurrency = currencyProvider.getCurrencyById(
            account.currencyId,
          );
          _selectedCategory = categoryProvider.getCategoryById(
            account.categoryId,
          );
        });
      });
    } else {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        final currencyProvider = context.read<CurrencyProvider>();
        final categoryProvider = context.read<CategoryProvider>();

        setState(() {
          _selectedCurrency = currencyProvider.defaultCurrency;
          _selectedCategory = categoryProvider.defaultCategory;
        });
      });
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _phoneController.dispose();
    _amountController.dispose();
    _detailsController.dispose();
    super.dispose();
  }

  Future<void> _selectDate() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _selectedDate,
      firstDate: DateTime(2020),
      lastDate: DateTime.now().add(const Duration(days: 1)),
      locale: const Locale('ar', 'SA'),
    );

    if (picked != null && picked != _selectedDate) {
      setState(() {
        _selectedDate = picked;
      });
    }
  }

  Future<void> _saveAccount() async {
    if (!_formKey.currentState!.validate()) return;

    if (_selectedCurrency == null) {
      _showErrorSnackBar('يرجى اختيار العملة');
      return;
    }
    if (_selectedCategory == null) {
      _showErrorSnackBar('يرجى اختيار الفئة');
      return;
    }
    if (_selectedDebtType == null) {
      _showErrorSnackBar('يرجى اختيار نوع الدين');
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final accountProvider = context.read<AccountProvider>();

      final account = Account(
        id: isEditing ? widget.accountToEdit!.id : null,
        name: _nameController.text.trim(),
        phoneNumber: _phoneController.text.trim(),
        amount: double.parse(_amountController.text.trim()),
        details:
            _detailsController.text.trim().isEmpty
                ? null
                : _detailsController.text.trim(),
        currencyId: _selectedCurrency!.id!,
        categoryId: _selectedCategory!.id!,
        date: _selectedDate,
        debtType: _selectedDebtType!,
        createdAt: isEditing ? widget.accountToEdit!.createdAt : DateTime.now(),
      );

      bool success;
      if (isEditing) {
        success = await accountProvider.updateAccount(account);
      } else {
        success = await accountProvider.addAccount(account);
      }

      if (success) {
        await _showSuccessAndClose();
      } else {
        _showErrorSnackBar(accountProvider.error ?? 'حدث خطأ غير متوقع');
      }
    } catch (e) {
      _showErrorSnackBar('خطأ: ${e.toString()}');
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _showSuccessAndClose() async {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          isEditing ? AppConstants.updateSuccess : AppConstants.saveSuccess,
        ),
        backgroundColor: Colors.green,
        duration: const Duration(seconds: 2),
      ),
    );

    await Future.delayed(const Duration(milliseconds: 500));
    if (mounted) {
      Navigator.of(context).pop(true);
    }
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  void _closeModal() {
    Navigator.of(context).pop(false);
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.9,
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
      ),
      child: Column(
        children: [
          _buildHeader(),
          Expanded(child: _buildForm()),
          _buildActionButtons(),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          Container(
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: Colors.grey[300],
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Icon(
                isEditing ? Icons.edit : Icons.add,
                color: Theme.of(context).colorScheme.primary,
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  isEditing
                      ? AppConstants.editAccount
                      : AppConstants.addAccount,
                  style: Theme.of(
                    context,
                  ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
                ),
              ),
              IconButton(onPressed: _closeModal, icon: const Icon(Icons.close)),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildForm() {
    return Form(
      key: _formKey,
      child: ListView(
        padding: const EdgeInsets.all(16),
        children: [
          CustomTextField(
            label: AppConstants.accountName,
            hint: 'أدخل اسم الحساب',
            controller: _nameController,
            validator: Validators.validateName,
            keyboardType: TextInputType.name,
            textDirection: TextDirection.rtl,
            prefixIcon: const Icon(Icons.person_outline),
          ),
          const SizedBox(height: 16),

          CustomTextField(
            label: AppConstants.phoneNumber,
            hint: '05xxxxxxxx',
            controller: _phoneController,
            validator: Validators.validatePhoneNumber,
            keyboardType: TextInputType.phone,
            inputFormatters: [PhoneNumberFormatter()],
            prefixIcon: const Icon(Icons.phone),
          ),
          const SizedBox(height: 16),

          Consumer<CategoryProvider>(
            builder: (context, categoryProvider, child) {
              return Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    AppConstants.category,
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(height: 8),
                  DropdownButtonFormField<model.Category>(
                    value: _selectedCategory,
                    items:
                        categoryProvider.categories.map((category) {
                          return DropdownMenuItem<model.Category>(
                            value: category,
                            child: Text(category.name),
                          );
                        }).toList(),
                    onChanged: (category) {
                      setState(() {
                        _selectedCategory = category;
                      });
                    },
                    validator:
                        (value) => Validators.validateRequired(
                          value,
                          AppConstants.category,
                        ),
                    decoration: InputDecoration(
                      hintText: 'اختر الفئة',
                      prefixIcon: const Icon(Icons.category),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    isExpanded: true,
                  ),
                ],
              );
            },
          ),
          const SizedBox(height: 16),

          CustomTextField(
            label: AppConstants.amount,
            hint: '0.00',
            controller: _amountController,
            validator: Validators.validateAmount,
            keyboardType: const TextInputType.numberWithOptions(decimal: true),
            inputFormatters: [AmountInputFormatter()],
            prefixIcon: const Icon(Icons.attach_money),
          ),
          const SizedBox(height: 16),

          Consumer<CurrencyProvider>(
            builder: (context, currencyProvider, child) {
              return Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    AppConstants.currency,
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(height: 8),
                  DropdownButtonFormField<Currency>(
                    value: _selectedCurrency,
                    items:
                        currencyProvider.currencies.map((currency) {
                          return DropdownMenuItem<Currency>(
                            value: currency,
                            child: Text('${currency.symbol} ${currency.name}'),
                          );
                        }).toList(),
                    onChanged: (currency) {
                      setState(() {
                        _selectedCurrency = currency;
                      });
                    },
                    validator:
                        (value) => Validators.validateRequired(
                          value,
                          AppConstants.currency,
                        ),
                    decoration: InputDecoration(
                      hintText: 'اختر العملة',
                      prefixIcon: const Icon(Icons.monetization_on),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    isExpanded: true,
                  ),
                ],
              );
            },
          ),
          const SizedBox(height: 16),

          DebtTypeSelector(
            selectedType: _selectedDebtType,
            onChanged: (type) {
              setState(() {
                _selectedDebtType = type;
              });
            },
            validator:
                (value) =>
                    Validators.validateRequired(value, AppConstants.debtType),
          ),
          const SizedBox(height: 16),

          CustomTextField(
            label: AppConstants.date,
            hint: 'اختر التاريخ',
            controller: TextEditingController(
              text: DateFormatter.formatDate(_selectedDate),
            ),
            readOnly: true,
            onTap: _selectDate,
            prefixIcon: const Icon(Icons.calendar_today),
            suffixIcon: const Icon(Icons.arrow_drop_down),
          ),
          const SizedBox(height: 16),

          CustomTextField(
            label: AppConstants.details,
            hint: 'أدخل تفاصيل إضافية (اختياري)',
            controller: _detailsController,
            validator: Validators.validateDetails,
            maxLines: 3,
            maxLength: AppConstants.maxDetailsLength,
            textDirection: TextDirection.rtl,
            prefixIcon: const Icon(Icons.notes),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: SafeArea(
        child: Row(
          children: [
            Expanded(
              child: OutlinedButton(
                onPressed: _isLoading ? null : _closeModal,
                child: Text(AppConstants.cancel),
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              flex: 2,
              child: ElevatedButton(
                onPressed: _isLoading ? null : _saveAccount,
                child:
                    _isLoading
                        ? const SizedBox(
                          height: 20,
                          width: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(
                              Colors.white,
                            ),
                          ),
                        )
                        : Text(AppConstants.save),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
