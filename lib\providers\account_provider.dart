import 'package:flutter/foundation.dart';
import '../models/account.dart';
import '../models/currency.dart';
import '../database/database_helper.dart';

class AccountProvider with ChangeNotifier {
  final DatabaseHelper _databaseHelper = DatabaseHelper();

  List<Account> _accounts = [];
  bool _isLoading = false;
  String? _error;

  List<Account> get accounts => _accounts;
  bool get isLoading => _isLoading;
  String? get error => _error;

  Future<void> loadAccounts() async {
    _setLoading(true);
    _setError(null);

    try {
      _accounts = await _databaseHelper.getAllAccounts();
      notifyListeners();
    } catch (e) {
      _setError('خطأ في تحميل الحسابات: ${e.toString()}');
    } finally {
      _setLoading(false);
    }
  }

  Future<void> loadAccountsByCategory(int categoryId) async {
    _setLoading(true);
    _setError(null);

    try {
      _accounts = await _databaseHelper.getAccountsByCategory(categoryId);
      notifyListeners();
    } catch (e) {
      _setError('خطأ في تحميل حسابات الفئة: ${e.toString()}');
    } finally {
      _setLoading(false);
    }
  }

  Future<void> loadAccountsByCategoryAndCurrency(
    int categoryId,
    int currencyId,
  ) async {
    _setLoading(true);
    _setError(null);

    try {
      _accounts = await _databaseHelper.getAccountsByCategoryAndCurrency(
        categoryId,
        currencyId,
      );
      notifyListeners();
    } catch (e) {
      _setError('خطأ في تحميل الحسابات: ${e.toString()}');
    } finally {
      _setLoading(false);
    }
  }

  Future<bool> addAccount(Account account) async {
    _setLoading(true);
    _setError(null);

    try {
      int id = await _databaseHelper.insertAccount(account);
      Account newAccount = account.copyWith(id: id);

      _accounts.insert(0, newAccount); // Add to beginning for newest first
      notifyListeners();

      return true;
    } catch (e) {
      _setError('خطأ في إضافة الحساب: ${e.toString()}');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  Future<bool> updateAccount(Account account) async {
    _setLoading(true);
    _setError(null);

    try {
      Account updatedAccount = account.copyWith(updatedAt: DateTime.now());
      await _databaseHelper.updateAccount(updatedAccount);

      int index = _accounts.indexWhere((a) => a.id == account.id);
      if (index != -1) {
        _accounts[index] = updatedAccount;
        notifyListeners();
      }

      return true;
    } catch (e) {
      _setError('خطأ في تحديث الحساب: ${e.toString()}');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  Future<bool> deleteAccount(int id) async {
    _setLoading(true);
    _setError(null);

    try {
      int result = await _databaseHelper.deleteAccount(id);

      if (result > 0) {
        _accounts.removeWhere((a) => a.id == id);
        notifyListeners();
        return true;
      } else {
        _setError('خطأ في حذف الحساب');
        return false;
      }
    } catch (e) {
      _setError('خطأ في حذف الحساب: ${e.toString()}');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  Account? getAccountById(int id) {
    try {
      return _accounts.firstWhere((account) => account.id == id);
    } catch (e) {
      return null;
    }
  }

  List<Account> getAccountsByCategory(int categoryId) {
    return _accounts
        .where((account) => account.categoryId == categoryId)
        .toList();
  }

  List<Account> getAccountsByCurrency(int currencyId) {
    return _accounts
        .where((account) => account.currencyId == currencyId)
        .toList();
  }

  List<Account> getAccountsByCategoryAndCurrency(
    int categoryId,
    int currencyId,
  ) {
    return _accounts
        .where(
          (account) =>
              account.categoryId == categoryId &&
              account.currencyId == currencyId,
        )
        .toList();
  }

  List<Account> getAccountsByDebtType(DebtType debtType) {
    return _accounts.where((account) => account.debtType == debtType).toList();
  }

  // Get currencies used in a specific category
  Future<List<Currency>> getCurrenciesUsedInCategory(int categoryId) async {
    try {
      return await _databaseHelper.getCurrenciesUsedInCategory(categoryId);
    } catch (e) {
      _setError('خطأ في تحميل العملات المستخدمة: ${e.toString()}');
      return [];
    }
  }

  // Calculate totals
  double getTotalAmountByCategory(int categoryId) {
    return _accounts
        .where((account) => account.categoryId == categoryId)
        .fold(0.0, (sum, account) => sum + account.amount);
  }

  double getTotalAmountByCategoryAndCurrency(int categoryId, int currencyId) {
    return _accounts
        .where(
          (account) =>
              account.categoryId == categoryId &&
              account.currencyId == currencyId,
        )
        .fold(0.0, (sum, account) => sum + account.amount);
  }

  double getTotalOwedToMe(int categoryId, int currencyId) {
    return _accounts
        .where(
          (account) =>
              account.categoryId == categoryId &&
              account.currencyId == currencyId &&
              account.debtType == DebtType.owedToMe,
        )
        .fold(0.0, (sum, account) => sum + account.amount);
  }

  double getTotalIOwe(int categoryId, int currencyId) {
    return _accounts
        .where(
          (account) =>
              account.categoryId == categoryId &&
              account.currencyId == currencyId &&
              account.debtType == DebtType.iOwe,
        )
        .fold(0.0, (sum, account) => sum + account.amount);
  }

  double getNetAmount(int categoryId, int currencyId) {
    double owedToMe = getTotalOwedToMe(categoryId, currencyId);
    double iOwe = getTotalIOwe(categoryId, currencyId);
    return owedToMe - iOwe;
  }

  // Search and filter
  List<Account> searchAccounts(String query) {
    if (query.isEmpty) return _accounts;

    String lowerQuery = query.toLowerCase();
    return _accounts
        .where(
          (account) =>
              account.name.toLowerCase().contains(lowerQuery) ||
              account.phoneNumber.contains(query) ||
              (account.details?.toLowerCase().contains(lowerQuery) ?? false),
        )
        .toList();
  }

  List<Account> filterAccountsByDateRange(
    DateTime startDate,
    DateTime endDate,
  ) {
    return _accounts
        .where(
          (account) =>
              account.date.isAfter(
                startDate.subtract(const Duration(days: 1)),
              ) &&
              account.date.isBefore(endDate.add(const Duration(days: 1))),
        )
        .toList();
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String? error) {
    _error = error;
    if (error != null) {
      notifyListeners();
    }
  }

  void clearError() {
    _error = null;
    notifyListeners();
  }
}
