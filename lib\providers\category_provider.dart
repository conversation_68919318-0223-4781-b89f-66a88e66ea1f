import 'package:flutter/foundation.dart';
import '../models/category.dart' as model;
import '../database/database_helper.dart';

class CategoryProvider with ChangeNotifier {
  final DatabaseHelper _databaseHelper = DatabaseHelper();

  List<model.Category> _categories = [];
  bool _isLoading = false;
  String? _error;

  List<model.Category> get categories => _categories;
  bool get isLoading => _isLoading;
  String? get error => _error;

  model.Category? get defaultCategory {
    if (_categories.isEmpty) return null;

    try {
      return _categories.firstWhere((category) => category.isDefault);
    } catch (e) {
      return _categories.first;
    }
  }

  bool get hasCategories => _categories.isNotEmpty;

  Future<void> loadCategories() async {
    _setLoading(true);
    _setError(null);

    try {
      _categories = await _databaseHelper.getAllCategories();
      notifyListeners();
    } catch (e) {
      _setError('خطأ في تحميل الفئات: ${e.toString()}');
    } finally {
      _setLoading(false);
    }
  }

  Future<bool> addCategory(model.Category category) async {
    _setLoading(true);
    _setError(null);

    try {
      // Check if category name already exists
      if (_categories.any(
        (c) => c.name.toLowerCase() == category.name.toLowerCase(),
      )) {
        _setError('اسم الفئة موجود مسبقاً');
        return false;
      }

      int id = await _databaseHelper.insertCategory(category);
      model.Category newCategory = category.copyWith(id: id);

      _categories.add(newCategory);
      _sortCategories();
      notifyListeners();

      return true;
    } catch (e) {
      _setError('خطأ في إضافة الفئة: ${e.toString()}');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  Future<bool> updateCategory(model.Category category) async {
    _setLoading(true);
    _setError(null);

    try {
      // Check if category name already exists (excluding current category)
      if (_categories.any(
        (c) =>
            c.id != category.id &&
            c.name.toLowerCase() == category.name.toLowerCase(),
      )) {
        _setError('اسم الفئة موجود مسبقاً');
        return false;
      }

      await _databaseHelper.updateCategory(category);

      int index = _categories.indexWhere((c) => c.id == category.id);
      if (index != -1) {
        _categories[index] = category;
        _sortCategories();
        notifyListeners();
      }

      return true;
    } catch (e) {
      _setError('خطأ في تحديث الفئة: ${e.toString()}');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  Future<bool> deleteCategory(int id) async {
    _setLoading(true);
    _setError(null);

    try {
      // Check if category is default
      model.Category? category =
          _categories.where((c) => c.id == id).firstOrNull;
      if (category?.isDefault == true) {
        _setError('لا يمكن حذف الفئة الافتراضية');
        return false;
      }

      int result = await _databaseHelper.deleteCategory(id);

      if (result > 0) {
        _categories.removeWhere((c) => c.id == id);
        notifyListeners();
        return true;
      } else {
        _setError('لا يمكن حذف الفئة - قد تحتوي على حسابات');
        return false;
      }
    } catch (e) {
      _setError('خطأ في حذف الفئة: ${e.toString()}');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  model.Category? getCategoryById(int id) {
    try {
      return _categories.where((category) => category.id == id).firstOrNull;
    } catch (e) {
      return null;
    }
  }

  model.Category? getCategoryByName(String name) {
    try {
      return _categories
          .where(
            (category) => category.name.toLowerCase() == name.toLowerCase(),
          )
          .firstOrNull;
    } catch (e) {
      return null;
    }
  }

  List<String> get existingCategoryNames {
    return _categories.map((c) => c.name).toList();
  }

  void _sortCategories() {
    _categories.sort((a, b) {
      // Default categories first
      if (a.isDefault && !b.isDefault) return -1;
      if (!a.isDefault && b.isDefault) return 1;

      // Then sort by name
      return a.name.compareTo(b.name);
    });
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String? error) {
    _error = error;
    if (error != null) {
      notifyListeners();
    }
  }

  void clearError() {
    _error = null;
    notifyListeners();
  }
}
