import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/currency.dart';
import '../providers/currency_provider.dart';
import '../widgets/custom_text_field.dart';
import '../utils/constants.dart';
import '../utils/validators.dart';

class CurrencyManagementScreen extends StatefulWidget {
  const CurrencyManagementScreen({super.key});

  @override
  State<CurrencyManagementScreen> createState() => _CurrencyManagementScreenState();
}

class _CurrencyManagementScreenState extends State<CurrencyManagementScreen> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<CurrencyProvider>().loadCurrencies();
    });
  }

  Future<void> _showAddCurrencyDialog() async {
    await showDialog(
      context: context,
      builder: (context) => const _CurrencyDialog(),
    );
  }

  Future<void> _showEditCurrencyDialog(Currency currency) async {
    await showDialog(
      context: context,
      builder: (context) => _CurrencyDialog(currencyToEdit: currency),
    );
  }

  Future<void> _deleteCurrency(Currency currency) async {
    if (currency.isDefault) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('لا يمكن حذف العملة الافتراضية'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: Text('هل أنت متأكد من حذف عملة "${currency.name}"؟\n\nسيتم حذف جميع الحسابات المرتبطة بهذه العملة.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text(AppConstants.cancel),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(
              foregroundColor: Theme.of(context).colorScheme.error,
            ),
            child: const Text(AppConstants.delete),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      final currencyProvider = context.read<CurrencyProvider>();
      final success = await currencyProvider.deleteCurrency(currency.id!);
      
      if (success) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text(AppConstants.deleteSuccess),
              backgroundColor: Colors.green,
            ),
          );
        }
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(currencyProvider.error ?? 'فشل في حذف العملة'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('إدارة العملات'),
        backgroundColor: Theme.of(context).colorScheme.surface,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: _showAddCurrencyDialog,
            tooltip: 'إضافة عملة جديدة',
          ),
        ],
      ),
      body: Consumer<CurrencyProvider>(
        builder: (context, currencyProvider, child) {
          if (currencyProvider.isLoading) {
            return const Center(child: CircularProgressIndicator());
          }

          if (currencyProvider.error != null) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.error_outline,
                    size: 64,
                    color: Theme.of(context).colorScheme.error,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    currencyProvider.error!,
                    style: Theme.of(context).textTheme.titleMedium,
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () => currencyProvider.loadCurrencies(),
                    child: const Text('إعادة المحاولة'),
                  ),
                ],
              ),
            );
          }

          if (currencyProvider.currencies.isEmpty) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.monetization_on_outlined,
                    size: 64,
                    color: Theme.of(context).colorScheme.onSurface.withOpacity(0.5),
                  ),
                  const SizedBox(height: 16),
                  Text(
                    AppConstants.noCurrencies,
                    style: Theme.of(context).textTheme.titleLarge,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'قم بإضافة عملة جديدة للبدء',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
                    ),
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton.icon(
                    onPressed: _showAddCurrencyDialog,
                    icon: const Icon(Icons.add),
                    label: const Text('إضافة عملة'),
                  ),
                ],
              ),
            );
          }

          return ListView.builder(
            padding: const EdgeInsets.all(AppConstants.defaultPadding),
            itemCount: currencyProvider.currencies.length,
            itemBuilder: (context, index) {
              final currency = currencyProvider.currencies[index];
              
              return Card(
                margin: const EdgeInsets.only(bottom: AppConstants.smallPadding),
                elevation: 2,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(AppConstants.defaultBorderRadius),
                ),
                child: ListTile(
                  leading: Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: currency.isDefault
                          ? Theme.of(context).colorScheme.primaryContainer
                          : Theme.of(context).colorScheme.secondaryContainer,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Text(
                      currency.symbol,
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: currency.isDefault
                            ? Theme.of(context).colorScheme.onPrimaryContainer
                            : Theme.of(context).colorScheme.onSecondaryContainer,
                      ),
                    ),
                  ),
                  title: Row(
                    children: [
                      Expanded(
                        child: Text(
                          currency.name,
                          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                      if (currency.isDefault)
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 8,
                            vertical: 2,
                          ),
                          decoration: BoxDecoration(
                            color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
                            borderRadius: BorderRadius.circular(4),
                            border: Border.all(
                              color: Theme.of(context).colorScheme.primary.withOpacity(0.3),
                            ),
                          ),
                          child: Text(
                            'افتراضي',
                            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              color: Theme.of(context).colorScheme.primary,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                    ],
                  ),
                  subtitle: Text(
                    'الكود: ${currency.code}',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
                    ),
                  ),
                  trailing: PopupMenuButton<String>(
                    onSelected: (value) {
                      switch (value) {
                        case 'edit':
                          _showEditCurrencyDialog(currency);
                          break;
                        case 'delete':
                          _deleteCurrency(currency);
                          break;
                      }
                    },
                    itemBuilder: (context) => [
                      const PopupMenuItem(
                        value: 'edit',
                        child: Row(
                          children: [
                            Icon(Icons.edit, size: 18),
                            SizedBox(width: 8),
                            Text(AppConstants.edit),
                          ],
                        ),
                      ),
                      if (!currency.isDefault)
                        PopupMenuItem(
                          value: 'delete',
                          child: Row(
                            children: [
                              Icon(
                                Icons.delete,
                                size: 18,
                                color: Theme.of(context).colorScheme.error,
                              ),
                              const SizedBox(width: 8),
                              Text(
                                AppConstants.delete,
                                style: TextStyle(
                                  color: Theme.of(context).colorScheme.error,
                                ),
                              ),
                            ],
                          ),
                        ),
                    ],
                  ),
                  contentPadding: const EdgeInsets.symmetric(
                    horizontal: AppConstants.defaultPadding,
                    vertical: AppConstants.smallPadding,
                  ),
                ),
              );
            },
          );
        },
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _showAddCurrencyDialog,
        child: const Icon(Icons.add),
        tooltip: 'إضافة عملة جديدة',
      ),
    );
  }
}

class _CurrencyDialog extends StatefulWidget {
  final Currency? currencyToEdit;

  const _CurrencyDialog({this.currencyToEdit});

  @override
  State<_CurrencyDialog> createState() => _CurrencyDialogState();
}

class _CurrencyDialogState extends State<_CurrencyDialog> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _symbolController = TextEditingController();
  final _codeController = TextEditingController();
  bool _isLoading = false;

  bool get isEditing => widget.currencyToEdit != null;

  @override
  void initState() {
    super.initState();
    if (isEditing) {
      _nameController.text = widget.currencyToEdit!.name;
      _symbolController.text = widget.currencyToEdit!.symbol;
      _codeController.text = widget.currencyToEdit!.code;
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _symbolController.dispose();
    _codeController.dispose();
    super.dispose();
  }

  Future<void> _saveCurrency() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final currencyProvider = context.read<CurrencyProvider>();
      
      final currency = Currency(
        id: isEditing ? widget.currencyToEdit!.id : null,
        name: _nameController.text.trim(),
        symbol: _symbolController.text.trim(),
        code: _codeController.text.trim().toUpperCase(),
        isDefault: isEditing ? widget.currencyToEdit!.isDefault : false,
        createdAt: isEditing ? widget.currencyToEdit!.createdAt : DateTime.now(),
      );

      bool success;
      if (isEditing) {
        success = await currencyProvider.updateCurrency(currency);
      } else {
        success = await currencyProvider.addCurrency(currency);
      }

      if (success) {
        if (mounted) {
          Navigator.of(context).pop();
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(isEditing ? AppConstants.updateSuccess : AppConstants.saveSuccess),
              backgroundColor: Colors.green,
            ),
          );
        }
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(currencyProvider.error ?? 'حدث خطأ غير متوقع'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text(isEditing ? 'تعديل العملة' : 'إضافة عملة جديدة'),
      content: Form(
        key: _formKey,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            CustomTextField(
              label: 'اسم العملة',
              hint: 'مثال: ريال سعودي',
              controller: _nameController,
              validator: (value) => Validators.validateCurrencyName(
                value,
                context.read<CurrencyProvider>().existingCurrencyNames
                    .where((name) => isEditing ? name != widget.currencyToEdit!.name : true)
                    .toList(),
              ),
              textDirection: TextDirection.rtl,
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            CustomTextField(
              label: 'رمز العملة',
              hint: 'مثال: ر.س',
              controller: _symbolController,
              validator: Validators.validateCurrencySymbol,
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            CustomTextField(
              label: 'كود العملة',
              hint: 'مثال: SAR',
              controller: _codeController,
              validator: (value) => Validators.validateCurrencyCode(
                value,
                context.read<CurrencyProvider>().existingCurrencyCodes
                    .where((code) => isEditing ? code != widget.currencyToEdit!.code.toUpperCase() : true)
                    .toList(),
              ),
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: _isLoading ? null : () => Navigator.of(context).pop(),
          child: const Text(AppConstants.cancel),
        ),
        ElevatedButton(
          onPressed: _isLoading ? null : _saveCurrency,
          child: _isLoading
              ? const SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(strokeWidth: 2),
                )
              : Text(AppConstants.save),
        ),
      ],
    );
  }
}
