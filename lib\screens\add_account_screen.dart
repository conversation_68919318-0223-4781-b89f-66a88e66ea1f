import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/account.dart';
import '../models/currency.dart';
import '../models/category.dart' as model;
import '../providers/account_provider.dart';
import '../providers/currency_provider.dart';
import '../providers/category_provider.dart';
import '../widgets/custom_text_field.dart';
import '../widgets/debt_type_selector.dart';
import '../utils/constants.dart';
import '../utils/validators.dart';
import '../utils/formatters.dart';

class AddAccountScreen extends StatefulWidget {
  final Account? accountToEdit;

  const AddAccountScreen({super.key, this.accountToEdit});

  @override
  State<AddAccountScreen> createState() => _AddAccountScreenState();
}

class _AddAccountScreenState extends State<AddAccountScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _phoneController = TextEditingController();
  final _amountController = TextEditingController();
  final _detailsController = TextEditingController();

  Currency? _selectedCurrency;
  model.Category? _selectedCategory;
  DebtType? _selectedDebtType;
  DateTime _selectedDate = DateTime.now();
  bool _isLoading = false;

  bool get isEditing => widget.accountToEdit != null;

  @override
  void initState() {
    super.initState();
    _initializeForm();
  }

  void _initializeForm() {
    if (isEditing) {
      final account = widget.accountToEdit!;
      _nameController.text = account.name;
      _phoneController.text = account.phoneNumber;
      _amountController.text = account.amount.toString();
      _detailsController.text = account.details ?? '';
      _selectedDebtType = account.debtType;
      _selectedDate = account.date;

      // Load currency and category from providers
      WidgetsBinding.instance.addPostFrameCallback((_) {
        final currencyProvider = context.read<CurrencyProvider>();
        final categoryProvider = context.read<CategoryProvider>();

        setState(() {
          _selectedCurrency = currencyProvider.getCurrencyById(
            account.currencyId,
          );
          _selectedCategory = categoryProvider.getCategoryById(
            account.categoryId,
          );
        });
      });
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _phoneController.dispose();
    _amountController.dispose();
    _detailsController.dispose();
    super.dispose();
  }

  Future<void> _selectDate() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _selectedDate,
      firstDate: DateTime(2020),
      lastDate: DateTime.now().add(const Duration(days: 1)),
      locale: const Locale('ar', 'SA'),
    );

    if (picked != null && picked != _selectedDate) {
      setState(() {
        _selectedDate = picked;
      });
    }
  }

  Future<void> _saveAccount() async {
    if (!_formKey.currentState!.validate()) return;
    if (_selectedCurrency == null ||
        _selectedCategory == null ||
        _selectedDebtType == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('يرجى ملء جميع الحقول المطلوبة')),
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final accountProvider = context.read<AccountProvider>();

      final account = Account(
        id: isEditing ? widget.accountToEdit!.id : null,
        name: _nameController.text.trim(),
        phoneNumber: _phoneController.text.trim(),
        amount: double.parse(_amountController.text.trim()),
        details:
            _detailsController.text.trim().isEmpty
                ? null
                : _detailsController.text.trim(),
        currencyId: _selectedCurrency!.id!,
        categoryId: _selectedCategory!.id!,
        date: _selectedDate,
        debtType: _selectedDebtType!,
        createdAt: isEditing ? widget.accountToEdit!.createdAt : DateTime.now(),
      );

      bool success;
      if (isEditing) {
        success = await accountProvider.updateAccount(account);
      } else {
        success = await accountProvider.addAccount(account);
      }

      if (success) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                isEditing
                    ? AppConstants.updateSuccess
                    : AppConstants.saveSuccess,
              ),
              backgroundColor: Colors.green,
            ),
          );
          Navigator.of(context).pop(true);
        }
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(accountProvider.error ?? 'حدث خطأ غير متوقع'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          isEditing ? AppConstants.editAccount : AppConstants.addAccount,
        ),
        backgroundColor: Theme.of(context).colorScheme.surface,
        elevation: 0,
        actions: [
          if (_isLoading)
            const Center(
              child: Padding(
                padding: EdgeInsets.all(16.0),
                child: SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(strokeWidth: 2),
                ),
              ),
            )
          else
            TextButton(
              onPressed: _saveAccount,
              child: Text(
                AppConstants.save,
                style: TextStyle(
                  color: Theme.of(context).colorScheme.primary,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: ListView(
          padding: const EdgeInsets.all(AppConstants.defaultPadding),
          children: [
            CustomTextField(
              label: AppConstants.accountName,
              hint: 'أدخل اسم الحساب',
              controller: _nameController,
              validator: Validators.validateName,
              keyboardType: TextInputType.name,
              textDirection: TextDirection.rtl,
            ),
            const SizedBox(height: AppConstants.defaultPadding),

            CustomTextField(
              label: AppConstants.phoneNumber,
              hint: '05xxxxxxxx',
              controller: _phoneController,
              validator: Validators.validatePhoneNumber,
              keyboardType: TextInputType.phone,
              inputFormatters: [PhoneNumberFormatter()],
              prefixIcon: const Icon(Icons.phone),
            ),
            const SizedBox(height: AppConstants.defaultPadding),

            CustomTextField(
              label: AppConstants.amount,
              hint: '0.00',
              controller: _amountController,
              validator: Validators.validateAmount,
              keyboardType: const TextInputType.numberWithOptions(
                decimal: true,
              ),
              inputFormatters: [AmountInputFormatter()],
              prefixIcon: const Icon(Icons.attach_money),
            ),
            const SizedBox(height: AppConstants.defaultPadding),

            Consumer<CurrencyProvider>(
              builder: (context, currencyProvider, child) {
                return CustomDropdownField<Currency>(
                  label: AppConstants.currency,
                  hint: 'اختر العملة',
                  value: _selectedCurrency,
                  items:
                      currencyProvider.currencies.map((currency) {
                        return DropdownMenuItem<Currency>(
                          value: currency,
                          child: Row(
                            children: [
                              Text(currency.symbol),
                              const SizedBox(width: 8),
                              Text(currency.name),
                            ],
                          ),
                        );
                      }).toList(),
                  onChanged: (currency) {
                    setState(() {
                      _selectedCurrency = currency;
                    });
                  },
                  validator:
                      (value) => Validators.validateRequired(
                        value,
                        AppConstants.currency,
                      ),
                  prefixIcon: const Icon(Icons.monetization_on),
                );
              },
            ),
            const SizedBox(height: AppConstants.defaultPadding),

            Consumer<CategoryProvider>(
              builder: (context, categoryProvider, child) {
                if (_selectedCategory == null &&
                    categoryProvider.categories.isNotEmpty) {
                  WidgetsBinding.instance.addPostFrameCallback((_) {
                    setState(() {
                      _selectedCategory = categoryProvider.defaultCategory;
                    });
                  });
                }

                return CustomDropdownField<model.Category>(
                  label: AppConstants.category,
                  hint: 'اختر الفئة',
                  value: _selectedCategory,
                  items:
                      categoryProvider.categories.map((category) {
                        return DropdownMenuItem<model.Category>(
                          value: category,
                          child: Text(category.name),
                        );
                      }).toList(),
                  onChanged: (category) {
                    setState(() {
                      _selectedCategory = category;
                    });
                  },
                  validator:
                      (value) => Validators.validateRequired(
                        value,
                        AppConstants.category,
                      ),
                  prefixIcon: const Icon(Icons.category),
                );
              },
            ),
            const SizedBox(height: AppConstants.defaultPadding),

            DebtTypeSelector(
              selectedType: _selectedDebtType,
              onChanged: (type) {
                setState(() {
                  _selectedDebtType = type;
                });
              },
              validator:
                  (value) =>
                      Validators.validateRequired(value, AppConstants.debtType),
            ),
            const SizedBox(height: AppConstants.defaultPadding),

            CustomTextField(
              label: AppConstants.date,
              hint: 'اختر التاريخ',
              controller: TextEditingController(
                text: DateFormatter.formatDate(_selectedDate),
              ),
              readOnly: true,
              onTap: _selectDate,
              prefixIcon: const Icon(Icons.calendar_today),
              suffixIcon: const Icon(Icons.arrow_drop_down),
            ),
            const SizedBox(height: AppConstants.defaultPadding),

            CustomTextField(
              label: AppConstants.details,
              hint: 'أدخل تفاصيل إضافية (اختياري)',
              controller: _detailsController,
              validator: Validators.validateDetails,
              maxLines: 3,
              maxLength: AppConstants.maxDetailsLength,
              textDirection: TextDirection.rtl,
            ),
            const SizedBox(height: AppConstants.largePadding),
          ],
        ),
      ),
    );
  }
}
