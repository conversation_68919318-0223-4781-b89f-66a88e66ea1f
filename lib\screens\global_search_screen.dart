import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/account.dart';
import '../models/currency.dart';
import '../models/category.dart' as model;
import '../providers/account_provider.dart';
import '../providers/currency_provider.dart';
import '../providers/category_provider.dart';
import '../widgets/account_table.dart';
import '../utils/constants.dart';
import 'account_details_screen.dart';

class GlobalSearchScreen extends StatefulWidget {
  const GlobalSearchScreen({super.key});

  @override
  State<GlobalSearchScreen> createState() => _GlobalSearchScreenState();
}

class _GlobalSearchScreenState extends State<GlobalSearchScreen> {
  final TextEditingController _searchController = TextEditingController();
  List<Account> _searchResults = [];
  bool _isSearching = false;
  String _currentQuery = '';

  @override
  void initState() {
    super.initState();
    _searchController.addListener(_onSearchChanged);
  }

  @override
  void dispose() {
    _searchController.removeListener(_onSearchChanged);
    _searchController.dispose();
    super.dispose();
  }

  void _onSearchChanged() {
    final query = _searchController.text.trim();
    if (query != _currentQuery) {
      _currentQuery = query;
      _performSearch(query);
    }
  }

  void _performSearch(String query) {
    setState(() {
      _isSearching = true;
    });

    if (query.isEmpty) {
      setState(() {
        _searchResults = [];
        _isSearching = false;
      });
      return;
    }

    final accountProvider = context.read<AccountProvider>();
    final results = accountProvider.searchAccounts(query);

    setState(() {
      _searchResults = results;
      _isSearching = false;
    });
  }

  Future<void> _navigateToAccountDetails(Account account) async {
    final currencyProvider = context.read<CurrencyProvider>();
    final categoryProvider = context.read<CategoryProvider>();
    
    final currency = currencyProvider.getCurrencyById(account.currencyId);
    final category = categoryProvider.getCategoryById(account.categoryId);

    if (currency != null && category != null) {
      final result = await Navigator.of(context).push(
        MaterialPageRoute(
          builder: (context) => AccountDetailsScreen(
            account: account,
            currency: currency,
            category: category,
          ),
        ),
      );

      if (result == true) {
        // Refresh search results
        _performSearch(_currentQuery);
      }
    }
  }

  Map<int, Currency> _getCurrencyMap() {
    final currencyProvider = context.read<CurrencyProvider>();
    final Map<int, Currency> currencyMap = {};
    for (final currency in currencyProvider.currencies) {
      if (currency.id != null) {
        currencyMap[currency.id!] = currency;
      }
    }
    return currencyMap;
  }

  Map<int, model.Category> _getCategoryMap() {
    final categoryProvider = context.read<CategoryProvider>();
    final Map<int, model.Category> categoryMap = {};
    for (final category in categoryProvider.categories) {
      if (category.id != null) {
        categoryMap[category.id!] = category;
      }
    }
    return categoryMap;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('البحث في الحسابات'),
        backgroundColor: Theme.of(context).colorScheme.surface,
        elevation: 0,
      ),
      body: Column(
        children: [
          // Search Bar
          Container(
            padding: const EdgeInsets.all(AppConstants.defaultPadding),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surface,
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.1),
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: TextField(
              controller: _searchController,
              autofocus: true,
              decoration: InputDecoration(
                hintText: 'ابحث في جميع الحسابات...',
                prefixIcon: const Icon(Icons.search),
                suffixIcon: _searchController.text.isNotEmpty
                    ? IconButton(
                        icon: const Icon(Icons.clear),
                        onPressed: () {
                          _searchController.clear();
                        },
                      )
                    : null,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(AppConstants.defaultBorderRadius),
                ),
                filled: true,
                fillColor: Theme.of(context).colorScheme.surfaceVariant.withValues(alpha: 0.5),
              ),
            ),
          ),

          // Search Results
          Expanded(
            child: _buildSearchResults(),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchResults() {
    if (_currentQuery.isEmpty) {
      return _buildSearchPrompt();
    }

    if (_isSearching) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    if (_searchResults.isEmpty) {
      return _buildNoResults();
    }

    return Consumer3<AccountProvider, CurrencyProvider, CategoryProvider>(
      builder: (context, accountProvider, currencyProvider, categoryProvider, child) {
        return SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Results Header
              Padding(
                padding: const EdgeInsets.all(AppConstants.defaultPadding),
                child: Row(
                  children: [
                    Icon(
                      Icons.search_outlined,
                      color: Theme.of(context).colorScheme.primary,
                    ),
                    const SizedBox(width: AppConstants.smallPadding),
                    Text(
                      'نتائج البحث',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const Spacer(),
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 4,
                      ),
                      decoration: BoxDecoration(
                        color: Theme.of(context).colorScheme.primaryContainer,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        '${_searchResults.length} نتيجة',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Theme.of(context).colorScheme.onPrimaryContainer,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ],
                ),
              ),

              // Results Table
              AccountTable(
                accounts: _searchResults,
                currencies: _getCurrencyMap(),
                categories: _getCategoryMap(),
                onAccountTap: _navigateToAccountDetails,
                showActions: false, // Hide actions in search results
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildSearchPrompt() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding * 2),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.search,
              size: 64,
              color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.5),
            ),
            const SizedBox(height: 16),
            Text(
              'ابحث في جميع الحسابات',
              style: Theme.of(context).textTheme.titleLarge,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              'يمكنك البحث بالاسم، رقم الهاتف، أو التفاصيل',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            _buildSearchTips(),
          ],
        ),
      ),
    );
  }

  Widget _buildSearchTips() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppConstants.defaultBorderRadius),
      ),
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'نصائح البحث:',
              style: Theme.of(context).textTheme.titleSmall?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            _buildTip('ابحث بالاسم الكامل أو جزء منه'),
            _buildTip('استخدم رقم الهاتف للبحث السريع'),
            _buildTip('ابحث في التفاصيل والملاحظات'),
            _buildTip('البحث يشمل جميع الفئات والعملات'),
          ],
        ),
      ),
    );
  }

  Widget _buildTip(String text) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 4),
      child: Row(
        children: [
          Icon(
            Icons.circle,
            size: 6,
            color: Theme.of(context).colorScheme.primary,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              text,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.8),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNoResults() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding * 2),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.search_off,
              size: 64,
              color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.5),
            ),
            const SizedBox(height: 16),
            Text(
              'لا توجد نتائج',
              style: Theme.of(context).textTheme.titleLarge,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              'لم يتم العثور على حسابات تطابق "${_currentQuery}"',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            Text(
              'جرب البحث بكلمات مختلفة أو تأكد من الإملاء',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}
