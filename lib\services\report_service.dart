import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:share_plus/share_plus.dart';
import 'package:path_provider/path_provider.dart';
import 'dart:io';
import '../models/report.dart';
import '../models/account.dart';
import '../models/personal_info.dart';
import '../providers/account_provider.dart';
import '../providers/personal_info_provider.dart';

import '../utils/excel_generator.dart';
import '../utils/pdf_generator.dart';
import '../utils/whatsapp_generator.dart';

class ReportService {
  Future<bool> generateAndExportReport(
    BuildContext context,
    ReportConfig config,
  ) async {
    try {
      debugPrint('🔄 Starting report generation for type: ${config.type}');

      // Get data
      final reportData = await _generateReportData(context, config);
      debugPrint('✅ Report data generated successfully');

      // Get personal info for PDF generation
      PersonalInfo? personalInfo;
      if (config.exportFormat == ExportFormat.pdf) {
        try {
          final personalInfoProvider = Provider.of<PersonalInfoProvider>(
            context,
            listen: false,
          );
          personalInfo = personalInfoProvider.personalInfo;
          if (personalInfo == null) {
            await personalInfoProvider.loadPersonalInfo();
            personalInfo = personalInfoProvider.personalInfo;
          }
          debugPrint(
            '📄 Personal info loaded for PDF: ${personalInfo?.fullName}',
          );
        } catch (e) {
          debugPrint('⚠️ Could not load personal info: $e');
        }
      }

      // Generate file based on format
      String? filePath;
      debugPrint('📄 Generating ${config.exportFormat.arabicName} file...');

      switch (config.exportFormat) {
        case ExportFormat.excel:
          filePath = await ExcelGenerator.generateReport(reportData);
          break;
        case ExportFormat.pdf:
          filePath = await PdfGenerator.generateReport(
            reportData,
            personalInfo: personalInfo,
          );
          break;
        case ExportFormat.whatsapp:
          filePath = await WhatsAppGenerator.generateReport(reportData);
          break;
      }

      if (filePath != null) {
        debugPrint('✅ File generated successfully at: $filePath');
        // Share the file
        await _shareFile(filePath, config.exportFormat);
        debugPrint('✅ File shared successfully');
        return true;
      } else {
        debugPrint('❌ Failed to generate file - filePath is null');
        return false;
      }
    } catch (e, stackTrace) {
      debugPrint('❌ Error generating report: $e');
      debugPrint('Stack trace: $stackTrace');
      return false;
    }
  }

  Future<ReportData> _generateReportData(
    BuildContext context,
    ReportConfig config,
  ) async {
    debugPrint('📊 Generating report data for type: ${config.type}');

    final accountProvider = Provider.of<AccountProvider>(
      context,
      listen: false,
    );

    debugPrint('📊 Loading accounts...');
    // Load accounts
    await accountProvider.loadAccounts();
    final accounts = accountProvider.accounts;
    debugPrint('📊 Loaded ${accounts.length} accounts');

    // Filter accounts by currency if specified
    final filteredAccounts =
        config.currency != null
            ? accounts
                .where((account) => account.currencyId == config.currency!.id)
                .toList()
            : accounts;

    debugPrint(
      '📊 Filtered to ${filteredAccounts.length} accounts for currency: ${config.currency?.name ?? "All"}',
    );

    // Generate data based on report type
    Map<String, dynamic> data = {};
    String title = config.type.arabicName;
    String subtitle = '';

    debugPrint('📊 Generating data for report type: ${config.type}');
    switch (config.type) {
      case ReportType.accountBalanceSummary:
        data = await _generateAccountBalanceSummary(filteredAccounts, config);
        subtitle = 'إجمالي الأرصدة للحسابات';
        break;
      case ReportType.detailedAccountBalance:
        data = await _generateDetailedAccountBalance(filteredAccounts, config);
        subtitle = 'تفاصيل الحسابات والمعاملات';
        break;
      case ReportType.monthlyAccountBalance:
        data = await _generateMonthlyAccountBalance(filteredAccounts, config);
        subtitle = 'الأرصدة الشهرية للحسابات';
        break;
      case ReportType.categoryAndCurrencySummary:
        data = await _generateCategoryAndCurrencySummary(accounts, config);
        subtitle = 'ملخص الفئات والعملات';
        break;
      case ReportType.monthlyCategoryDetail:
        data = await _generateMonthlyCategoryDetail(filteredAccounts, config);
        subtitle = 'التفاصيل الشهرية للفئة';
        break;
    }

    debugPrint('📊 Report data generated successfully');
    return ReportData(
      title: title,
      subtitle: subtitle,
      generatedAt: DateTime.now(),
      data: data,
      config: config,
    );
  }

  Future<Map<String, dynamic>> _generateAccountBalanceSummary(
    List<Account> accounts,
    ReportConfig config,
  ) async {
    double totalOwedToMe = 0;
    double totalIOwe = 0;

    final accountSummaries = <Map<String, dynamic>>[];

    for (final account in accounts) {
      final balance = account.amount;

      if (balance > 0) {
        totalOwedToMe += balance;
      } else if (balance < 0) {
        totalIOwe += balance.abs();
      }

      accountSummaries.add({
        'name': account.name,
        'phone': account.phoneNumber,
        'address': account.details ?? '',
        'balance': balance,
        'debtType': account.debtType,
        'currency': config.currency?.symbol ?? '',
      });
    }

    final netBalance = totalOwedToMe - totalIOwe;

    return {
      'accounts': accountSummaries,
      'summary': {
        'totalOwedToMe': totalOwedToMe,
        'totalIOwe': totalIOwe,
        'netBalance': netBalance,
        'accountCount': accounts.length,
      },
      'currency': config.currency?.toMap(),
    };
  }

  Future<Map<String, dynamic>> _generateDetailedAccountBalance(
    List<Account> accounts,
    ReportConfig config,
  ) async {
    final transactions = <Map<String, dynamic>>[];
    double totalOwedToMe = 0;
    double totalIOwe = 0;

    // Create transaction entries from accounts
    for (final account in accounts) {
      final amount = account.amount.abs();

      transactions.add({
        'accountName': account.name,
        'amount': amount,
        'type': account.debtType == DebtType.owedToMe ? 'له' : 'عليه',
        'details': account.details ?? '',
        'date': account.date,
        'currency': config.currency?.symbol ?? '',
      });

      // Calculate totals
      if (account.debtType == DebtType.owedToMe) {
        totalOwedToMe += amount;
      } else {
        totalIOwe += amount;
      }
    }

    // Sort transactions by date (newest first)
    transactions.sort(
      (a, b) => (b['date'] as DateTime).compareTo(a['date'] as DateTime),
    );

    return {
      'transactions': transactions,
      'summary': {
        'totalOwedToMe': totalOwedToMe,
        'totalIOwe': totalIOwe,
        'netBalance': totalOwedToMe - totalIOwe,
      },
      'currency': config.currency?.toMap(),
    };
  }

  Future<Map<String, dynamic>> _generateMonthlyAccountBalance(
    List<Account> accounts,
    ReportConfig config,
  ) async {
    // This would require transaction history to generate monthly data
    // For now, we'll return current balances grouped by month
    final monthlyData = <String, Map<String, dynamic>>{};

    if (config.startDate != null && config.endDate != null) {
      final startDate = config.startDate!;
      final endDate = config.endDate!;

      // Generate monthly summaries (simplified)
      DateTime current = DateTime(startDate.year, startDate.month);
      while (current.isBefore(endDate) || current.isAtSameMomentAs(endDate)) {
        final monthKey =
            '${current.year}-${current.month.toString().padLeft(2, '0')}';

        monthlyData[monthKey] = {
          'month': monthKey,
          'totalOwedToMe': 0.0,
          'totalIOwe': 0.0,
          'netBalance': 0.0,
          'accountCount': accounts.length,
        };

        current = DateTime(current.year, current.month + 1);
      }
    }

    return {
      'monthlyData': monthlyData,
      'dateRange': {
        'start': config.startDate?.toIso8601String(),
        'end': config.endDate?.toIso8601String(),
      },
      'currency': config.currency?.toMap(),
    };
  }

  Future<Map<String, dynamic>> _generateCategoryAndCurrencySummary(
    List<Account> accounts,
    ReportConfig config,
  ) async {
    final categoryData = <String, Map<String, dynamic>>{};
    final currencyData = <String, Map<String, dynamic>>{};

    for (final account in accounts) {
      // Group by category
      final categoryKey = account.categoryId.toString();
      if (!categoryData.containsKey(categoryKey)) {
        categoryData[categoryKey] = {
          'categoryId': account.categoryId,
          'totalOwedToMe': 0.0,
          'totalIOwe': 0.0,
          'accountCount': 0,
        };
      }

      final balance = account.amount;
      if (balance > 0) {
        categoryData[categoryKey]!['totalOwedToMe'] += balance;
      } else if (balance < 0) {
        categoryData[categoryKey]!['totalIOwe'] += balance.abs();
      }
      categoryData[categoryKey]!['accountCount']++;

      // Group by currency
      final currencyKey = account.currencyId.toString();
      if (!currencyData.containsKey(currencyKey)) {
        currencyData[currencyKey] = {
          'currencyId': account.currencyId,
          'totalOwedToMe': 0.0,
          'totalIOwe': 0.0,
          'accountCount': 0,
        };
      }

      if (balance > 0) {
        currencyData[currencyKey]!['totalOwedToMe'] += balance;
      } else if (balance < 0) {
        currencyData[currencyKey]!['totalIOwe'] += balance.abs();
      }
      currencyData[currencyKey]!['accountCount']++;
    }

    return {
      'categoryData': categoryData,
      'currencyData': currencyData,
      'totalAccounts': accounts.length,
    };
  }

  Future<Map<String, dynamic>> _generateMonthlyCategoryDetail(
    List<Account> accounts,
    ReportConfig config,
  ) async {
    // Filter by category if specified
    final filteredAccounts =
        config.categoryId != null
            ? accounts
                .where(
                  (account) =>
                      account.categoryId.toString() == config.categoryId,
                )
                .toList()
            : accounts;

    return await _generateMonthlyAccountBalance(filteredAccounts, config);
  }

  Future<void> _shareFile(String filePath, ExportFormat format) async {
    final file = File(filePath);
    if (await file.exists()) {
      await Share.shareXFiles(
        [XFile(filePath)],
        text: 'تقرير الحسابات',
        subject: 'تقرير الحسابات',
      );
    }
  }
}
