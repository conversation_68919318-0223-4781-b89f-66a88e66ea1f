import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/account.dart';
import '../models/currency.dart';
import '../models/category.dart' as model;
import '../models/transaction.dart';
import '../providers/account_provider.dart';
import '../providers/currency_provider.dart';
import '../providers/category_provider.dart';
import '../widgets/simple_add_account_modal.dart';
import '../widgets/add_transaction_modal.dart';
import '../widgets/debt_type_selector.dart';
import '../utils/constants.dart';
import '../utils/formatters.dart';

class AccountDetailsScreen extends StatefulWidget {
  final Account account;
  final Currency currency;
  final model.Category category;

  const AccountDetailsScreen({
    super.key,
    required this.account,
    required this.currency,
    required this.category,
  });

  @override
  State<AccountDetailsScreen> createState() => _AccountDetailsScreenState();
}

class _AccountDetailsScreenState extends State<AccountDetailsScreen> {
  List<Transaction> _transactions = [];
  bool _isLoading = false;
  Account? _currentAccount;

  @override
  void initState() {
    super.initState();
    _currentAccount = widget.account;
    _loadTransactions();
  }

  Future<void> _loadTransactions() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // TODO: Load transactions from database
      // For now, create some demo transactions
      _transactions = _generateDemoTransactions();
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحميل المعاملات: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  List<Transaction> _generateDemoTransactions() {
    // Generate demo transactions for demonstration
    return [
      Transaction(
        id: 1,
        accountId: widget.account.id!,
        amount: 500.0,
        description: 'دفعة جزئية',
        date: DateTime.now().subtract(const Duration(days: 2)),
        type: TransactionType.owedToMe,
      ),
      Transaction(
        id: 2,
        accountId: widget.account.id!,
        amount: -200.0,
        description: 'دفعة للحساب',
        date: DateTime.now().subtract(const Duration(days: 5)),
        type: TransactionType.iOwe,
      ),
      Transaction(
        id: 3,
        accountId: widget.account.id!,
        amount: 100.0,
        description: 'تعديل الرصيد',
        date: DateTime.now().subtract(const Duration(days: 7)),
        type: TransactionType.owedToMe,
      ),
    ];
  }

  Future<void> _editAccount() async {
    final result = await SimpleAddAccountModal.show(
      context,
      accountToEdit: _currentAccount,
    );

    if (result == true && mounted) {
      // Reload account data
      final accountProvider = context.read<AccountProvider>();
      final updatedAccounts = accountProvider.accounts;
      final updatedAccount = updatedAccounts.firstWhere(
        (acc) => acc.id == _currentAccount!.id,
        orElse: () => _currentAccount!,
      );

      setState(() {
        _currentAccount = updatedAccount;
      });
    }
  }

  Future<void> _deleteAccount() async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('تأكيد الحذف'),
            content: Text(
              'هل أنت متأكد من حذف حساب "${_currentAccount!.name}"؟',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(false),
                child: const Text('إلغاء'),
              ),
              TextButton(
                onPressed: () => Navigator.of(context).pop(true),
                style: TextButton.styleFrom(foregroundColor: Colors.red),
                child: const Text('حذف'),
              ),
            ],
          ),
    );

    if (confirmed == true && mounted) {
      final accountProvider = context.read<AccountProvider>();
      final success = await accountProvider.deleteAccount(_currentAccount!.id!);

      if (success && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم حذف الحساب بنجاح'),
            backgroundColor: Colors.green,
          ),
        );
        Navigator.of(context).pop(true); // Return to previous screen
      } else if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(accountProvider.error ?? 'فشل في حذف الحساب'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _addTransaction() async {
    final transaction = await AddTransactionModal.show(
      context,
      account: _currentAccount!,
      currency: widget.currency,
    );

    if (transaction != null) {
      // Add the transaction to the list
      setState(() {
        _transactions.insert(
          0,
          transaction,
        ); // Add to beginning for newest first
      });

      // TODO: Update account balance in database
      // For now, just update the current account amount
      final newAmount = _currentAccount!.amount + transaction.amount;
      setState(() {
        _currentAccount = _currentAccount!.copyWith(amount: newAmount);
      });

      // Reload transactions to ensure consistency
      await _loadTransactions();
    }
  }

  Future<void> _editTransaction(Transaction transaction) async {
    // Create a new transaction modal for editing
    final editedTransaction = await AddTransactionModal.show(
      context,
      account: _currentAccount!,
      currency: widget.currency,
    );

    if (editedTransaction != null) {
      // Remove the old transaction and add the edited one
      setState(() {
        final index = _transactions.indexWhere((t) => t.id == transaction.id);
        if (index != -1) {
          _transactions[index] = editedTransaction.copyWith(id: transaction.id);
        }
      });

      // Update account balance by removing old transaction effect and adding new one
      final balanceChange = editedTransaction.amount - transaction.amount;
      setState(() {
        _currentAccount = _currentAccount!.copyWith(
          amount: _currentAccount!.amount + balanceChange,
        );
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم تعديل المعاملة بنجاح'),
            backgroundColor: Colors.green,
          ),
        );
      }
    }
  }

  Future<void> _deleteTransaction(Transaction transaction) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('تأكيد الحذف'),
            content: const Text('هل أنت متأكد من حذف هذه المعاملة؟'),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(false),
                child: const Text('إلغاء'),
              ),
              TextButton(
                onPressed: () => Navigator.of(context).pop(true),
                style: TextButton.styleFrom(foregroundColor: Colors.red),
                child: const Text('حذف'),
              ),
            ],
          ),
    );

    if (confirmed == true) {
      // Remove transaction from list
      setState(() {
        _transactions.removeWhere((t) => t.id == transaction.id);
      });

      // Update account balance by reversing the transaction effect
      setState(() {
        _currentAccount = _currentAccount!.copyWith(
          amount: _currentAccount!.amount - transaction.amount,
        );
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم حذف المعاملة بنجاح'),
            backgroundColor: Colors.green,
          ),
        );
      }
    }
  }

  double get _totalOwedToMe {
    final accountOwed =
        _currentAccount!.debtType == DebtType.owedToMe
            ? _currentAccount!.amount
            : 0.0;
    final transactionOwed = _transactions
        .where((t) => t.type == TransactionType.owedToMe)
        .fold(0.0, (sum, t) => sum + t.amount.abs());
    return accountOwed + transactionOwed;
  }

  double get _totalIOwe {
    final accountOwed =
        _currentAccount!.debtType == DebtType.iOwe
            ? _currentAccount!.amount
            : 0.0;
    final transactionOwed = _transactions
        .where((t) => t.type == TransactionType.iOwe)
        .fold(0.0, (sum, t) => sum + t.amount.abs());
    return accountOwed + transactionOwed;
  }

  double get _netBalance {
    return _totalOwedToMe - _totalIOwe;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(_currentAccount!.name),
        backgroundColor: Theme.of(context).colorScheme.surface,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.edit),
            onPressed: _editAccount,
            tooltip: 'تعديل الحساب',
          ),
          PopupMenuButton<String>(
            onSelected: (value) {
              if (value == 'delete') {
                _deleteAccount();
              }
            },
            itemBuilder:
                (context) => [
                  const PopupMenuItem(
                    value: 'delete',
                    child: Row(
                      children: [
                        Icon(Icons.delete, color: Colors.red),
                        SizedBox(width: 8),
                        Text('حذف الحساب', style: TextStyle(color: Colors.red)),
                      ],
                    ),
                  ),
                ],
          ),
        ],
      ),
      body: RefreshIndicator(
        onRefresh: _loadTransactions,
        child: CustomScrollView(
          slivers: [
            // Account Info Card
            SliverToBoxAdapter(child: _buildAccountInfoCard()),

            // Balance Summary Card
            SliverToBoxAdapter(child: _buildBalanceSummaryCard()),

            // Transactions Header
            SliverToBoxAdapter(child: _buildTransactionsHeader()),

            // Transactions List
            if (_isLoading)
              const SliverFillRemaining(
                child: Center(child: CircularProgressIndicator()),
              )
            else if (_transactions.isEmpty)
              SliverFillRemaining(child: _buildEmptyTransactions())
            else
              SliverPadding(
                padding: const EdgeInsets.symmetric(
                  horizontal: AppConstants.defaultPadding,
                ),
                sliver: SliverList(
                  delegate: SliverChildBuilderDelegate((context, index) {
                    final transaction = _transactions[index];
                    return _buildTransactionCard(transaction);
                  }, childCount: _transactions.length),
                ),
              ),
          ],
        ),
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: _addTransaction,
        icon: const Icon(Icons.add),
        label: const Text('إضافة معاملة'),
        tooltip: 'إضافة معاملة جديدة',
      ),
    );
  }

  Widget _buildAccountInfoCard() {
    return Card(
      margin: const EdgeInsets.all(AppConstants.defaultPadding),
      elevation: 4,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppConstants.defaultBorderRadius),
      ),
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Expanded(
                  child: Text(
                    'معلومات الحساب',
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                DebtTypeBadge(debtType: _currentAccount!.debtType),
              ],
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            _buildInfoRow('الاسم', _currentAccount!.name, Icons.person),
            _buildInfoRow(
              'رقم الهاتف',
              _currentAccount!.phoneNumber,
              Icons.phone,
            ),
            _buildInfoRow('الفئة', widget.category.name, Icons.category),
            _buildInfoRow(
              'العملة',
              '${widget.currency.name} (${widget.currency.symbol})',
              Icons.monetization_on,
            ),
            _buildInfoRow(
              'التاريخ',
              DateFormatter.formatDate(_currentAccount!.date),
              Icons.calendar_today,
            ),
            if (_currentAccount!.details != null &&
                _currentAccount!.details!.isNotEmpty)
              _buildInfoRow('التفاصيل', _currentAccount!.details!, Icons.notes),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value, IconData icon) {
    return Padding(
      padding: const EdgeInsets.only(bottom: AppConstants.smallPadding),
      child: Row(
        children: [
          Icon(
            icon,
            size: 20,
            color: Theme.of(
              context,
            ).colorScheme.onSurface.withValues(alpha: 0.6),
          ),
          const SizedBox(width: AppConstants.smallPadding),
          Expanded(
            flex: 2,
            child: Text(
              label,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w500,
                color: Theme.of(
                  context,
                ).colorScheme.onSurface.withValues(alpha: 0.8),
              ),
            ),
          ),
          Expanded(
            flex: 3,
            child: Text(
              value,
              style: Theme.of(context).textTheme.bodyMedium,
              textAlign: TextAlign.end,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBalanceSummaryCard() {
    return Card(
      margin: const EdgeInsets.symmetric(
        horizontal: AppConstants.defaultPadding,
        vertical: AppConstants.smallPadding,
      ),
      elevation: 4,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppConstants.defaultBorderRadius),
      ),
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'ملخص الرصيد',
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            Row(
              children: [
                Expanded(
                  child: _buildBalanceItem(
                    'له',
                    _totalOwedToMe,
                    Icons.trending_up,
                    Colors.green,
                  ),
                ),
                const SizedBox(width: AppConstants.defaultPadding),
                Expanded(
                  child: _buildBalanceItem(
                    'عليه',
                    _totalIOwe,
                    Icons.trending_down,
                    Colors.red,
                  ),
                ),
              ],
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(AppConstants.defaultPadding),
              decoration: BoxDecoration(
                color:
                    _netBalance >= 0
                        ? Colors.green.withValues(alpha: 0.1)
                        : Colors.red.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(
                  AppConstants.smallBorderRadius,
                ),
                border: Border.all(
                  color: _netBalance >= 0 ? Colors.green : Colors.red,
                  width: 1,
                ),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    children: [
                      Icon(
                        Icons.account_balance_wallet,
                        color: _netBalance >= 0 ? Colors.green : Colors.red,
                        size: 20,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        'الصافي',
                        style: Theme.of(
                          context,
                        ).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: _netBalance >= 0 ? Colors.green : Colors.red,
                        ),
                      ),
                    ],
                  ),
                  Text(
                    CurrencyFormatter.formatAmount(
                      _netBalance.abs(),
                      widget.currency,
                    ),
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: _netBalance >= 0 ? Colors.green : Colors.red,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBalanceItem(
    String label,
    double amount,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(AppConstants.smallPadding),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(AppConstants.smallBorderRadius),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 4),
          Text(
            label,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: color,
              fontWeight: FontWeight.w600,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 4),
          Text(
            CurrencyFormatter.formatAmount(amount.abs(), widget.currency),
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: color,
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildTransactionsHeader() {
    return Padding(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: Row(
        children: [
          Icon(Icons.history, color: Theme.of(context).colorScheme.primary),
          const SizedBox(width: AppConstants.smallPadding),
          Text(
            'سجل المعاملات',
            style: Theme.of(
              context,
            ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
          ),
          const Spacer(),
          Text(
            '${_transactions.length} معاملة',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Theme.of(
                context,
              ).colorScheme.onSurface.withValues(alpha: 0.6),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyTransactions() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.receipt_long_outlined,
            size: 64,
            color: Theme.of(
              context,
            ).colorScheme.onSurface.withValues(alpha: 0.5),
          ),
          const SizedBox(height: 16),
          Text(
            'لا توجد معاملات',
            style: Theme.of(context).textTheme.titleMedium,
          ),
          const SizedBox(height: 8),
          Text(
            'قم بإضافة معاملة جديدة لتتبع التغييرات',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Theme.of(
                context,
              ).colorScheme.onSurface.withValues(alpha: 0.7),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildTransactionCard(Transaction transaction) {
    final isPositive = transaction.amount >= 0;
    final color = isPositive ? Colors.green : Colors.red;

    return Card(
      margin: const EdgeInsets.only(bottom: AppConstants.smallPadding),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppConstants.defaultBorderRadius),
      ),
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: color.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    _getTransactionIcon(transaction.type),
                    color: color,
                    size: 20,
                  ),
                ),
                const SizedBox(width: AppConstants.smallPadding),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        transaction.typeArabic,
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      Text(
                        DateFormatter.formatDate(transaction.date),
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Theme.of(
                            context,
                          ).colorScheme.onSurface.withValues(alpha: 0.6),
                        ),
                      ),
                    ],
                  ),
                ),
                Text(
                  '${isPositive ? '+' : ''}${CurrencyFormatter.formatAmount(transaction.amount, widget.currency)}',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: color,
                  ),
                ),
                const SizedBox(width: 8),
                PopupMenuButton<String>(
                  onSelected: (value) {
                    if (value == 'edit') {
                      _editTransaction(transaction);
                    } else if (value == 'delete') {
                      _deleteTransaction(transaction);
                    }
                  },
                  itemBuilder:
                      (context) => [
                        const PopupMenuItem(
                          value: 'edit',
                          child: Row(
                            children: [
                              Icon(Icons.edit, size: 16, color: Colors.blue),
                              SizedBox(width: 8),
                              Text('تعديل'),
                            ],
                          ),
                        ),
                        const PopupMenuItem(
                          value: 'delete',
                          child: Row(
                            children: [
                              Icon(Icons.delete, size: 16, color: Colors.red),
                              SizedBox(width: 8),
                              Text('حذف', style: TextStyle(color: Colors.red)),
                            ],
                          ),
                        ),
                      ],
                  child: Container(
                    padding: const EdgeInsets.all(4),
                    decoration: BoxDecoration(
                      color: Colors.grey.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: const Icon(
                      Icons.more_vert,
                      size: 16,
                      color: Colors.grey,
                    ),
                  ),
                ),
              ],
            ),
            if (transaction.description != null &&
                transaction.description!.isNotEmpty) ...[
              const SizedBox(height: AppConstants.smallPadding),
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(AppConstants.smallPadding),
                decoration: BoxDecoration(
                  color: Theme.of(
                    context,
                  ).colorScheme.surfaceVariant.withValues(alpha: 0.5),
                  borderRadius: BorderRadius.circular(
                    AppConstants.smallBorderRadius,
                  ),
                ),
                child: Text(
                  transaction.description!,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Theme.of(
                      context,
                    ).colorScheme.onSurface.withValues(alpha: 0.8),
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  IconData _getTransactionIcon(TransactionType type) {
    switch (type) {
      case TransactionType.owedToMe:
        return Icons.trending_up;
      case TransactionType.iOwe:
        return Icons.trending_down;
    }
  }
}
