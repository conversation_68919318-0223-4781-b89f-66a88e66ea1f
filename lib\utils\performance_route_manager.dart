import 'package:flutter/material.dart';

/// Performance-optimized route manager for smooth navigation
class PerformanceRouteManager {
  static const Duration _transitionDuration = Duration(milliseconds: 250);
  
  /// Create a performance-optimized page route with custom transitions
  static PageRoute<T> createRoute<T extends Object?>(
    Widget page, {
    RouteSettings? settings,
    bool maintainState = true,
    bool fullscreenDialog = false,
    Duration? transitionDuration,
  }) {
    return PageRouteBuilder<T>(
      settings: settings,
      pageBuilder: (context, animation, secondaryAnimation) => page,
      maintainState: maintainState,
      fullscreenDialog: fullscreenDialog,
      transitionDuration: transitionDuration ?? _transitionDuration,
      reverseTransitionDuration: transitionDuration ?? _transitionDuration,
      transitionsBuilder: (context, animation, secondaryAnimation, child) {
        // Use optimized slide transition for better performance
        const begin = Offset(1.0, 0.0);
        const end = Offset.zero;
        const curve = Curves.easeInOutCubic;

        var tween = Tween(begin: begin, end: end).chain(
          CurveTween(curve: curve),
        );

        return SlideTransition(
          position: animation.drive(tween),
          child: child,
        );
      },
    );
  }

  /// Create a modal route with fade transition for dialogs
  static PageRoute<T> createModalRoute<T extends Object?>(
    Widget page, {
    RouteSettings? settings,
    bool barrierDismissible = true,
    Color? barrierColor,
  }) {
    return PageRouteBuilder<T>(
      settings: settings,
      pageBuilder: (context, animation, secondaryAnimation) => page,
      fullscreenDialog: true,
      opaque: false,
      barrierDismissible: barrierDismissible,
      barrierColor: barrierColor ?? Colors.black54,
      transitionDuration: const Duration(milliseconds: 200),
      reverseTransitionDuration: const Duration(milliseconds: 200),
      transitionsBuilder: (context, animation, secondaryAnimation, child) {
        return FadeTransition(
          opacity: animation,
          child: ScaleTransition(
            scale: Tween<double>(
              begin: 0.8,
              end: 1.0,
            ).animate(CurvedAnimation(
              parent: animation,
              curve: Curves.easeOutCubic,
            )),
            child: child,
          ),
        );
      },
    );
  }

  /// Navigate to a page with performance optimization
  static Future<T?> navigateTo<T extends Object?>(
    BuildContext context,
    Widget page, {
    bool replace = false,
    bool clearStack = false,
    RouteSettings? settings,
  }) {
    final route = createRoute<T>(page, settings: settings);

    if (clearStack) {
      return Navigator.of(context).pushAndRemoveUntil(
        route,
        (route) => false,
      );
    } else if (replace) {
      return Navigator.of(context).pushReplacement(route);
    } else {
      return Navigator.of(context).push(route);
    }
  }

  /// Show a modal with performance optimization
  static Future<T?> showModal<T extends Object?>(
    BuildContext context,
    Widget page, {
    bool barrierDismissible = true,
    Color? barrierColor,
    RouteSettings? settings,
  }) {
    final route = createModalRoute<T>(
      page,
      settings: settings,
      barrierDismissible: barrierDismissible,
      barrierColor: barrierColor,
    );

    return Navigator.of(context).push(route);
  }

  /// Pre-warm a route for faster navigation
  static void preWarmRoute(BuildContext context, Widget page) {
    // Pre-build the widget tree to reduce first-frame jank
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (context.mounted) {
        // Create a temporary render tree to warm up the widget
        final renderObject = page.createElement();
        renderObject.mount(null, null);
        renderObject.unmount();
      }
    });
  }
}

/// Mixin for widgets that need performance optimization
mixin PerformanceOptimizedWidget on Widget {
  /// Override to provide performance hints
  bool get wantKeepAlive => false;
  
  /// Override to control rebuild frequency
  bool get shouldRebuild => true;
}

/// Performance-optimized StatefulWidget base class
abstract class PerformanceStatefulWidget extends StatefulWidget {
  const PerformanceStatefulWidget({super.key});
  
  /// Override to control when the widget should rebuild
  bool shouldRebuild(covariant PerformanceStatefulWidget oldWidget) => true;
}

/// Performance-optimized State base class
abstract class PerformanceState<T extends PerformanceStatefulWidget> 
    extends State<T> with AutomaticKeepAliveClientMixin {
  
  @override
  bool get wantKeepAlive => false;
  
  /// Debounced setState to prevent excessive rebuilds
  void debouncedSetState(VoidCallback fn, {Duration delay = const Duration(milliseconds: 16)}) {
    Future.delayed(delay, () {
      if (mounted) {
        setState(fn);
      }
    });
  }
  
  /// Throttled setState to limit rebuild frequency
  DateTime? _lastSetStateTime;
  void throttledSetState(VoidCallback fn, {Duration throttle = const Duration(milliseconds: 100)}) {
    final now = DateTime.now();
    if (_lastSetStateTime == null || now.difference(_lastSetStateTime!) >= throttle) {
      _lastSetStateTime = now;
      if (mounted) {
        setState(fn);
      }
    }
  }
}
