import 'package:flutter/material.dart';
import '../models/transaction.dart';
import '../models/account.dart';
import '../models/currency.dart';
import '../widgets/custom_text_field.dart';
import '../utils/constants.dart';
import '../utils/validators.dart';
import '../utils/formatters.dart';

class AddTransactionModal {
  static Future<Transaction?> show(
    BuildContext context, {
    required Account account,
    required Currency currency,
  }) {
    return showModalBottomSheet<Transaction>(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      enableDrag: true,
      isDismissible: true,
      builder:
          (context) =>
              _AddTransactionModalContent(account: account, currency: currency),
    );
  }
}

class _AddTransactionModalContent extends StatefulWidget {
  final Account account;
  final Currency currency;

  const _AddTransactionModalContent({
    required this.account,
    required this.currency,
  });

  @override
  State<_AddTransactionModalContent> createState() =>
      _AddTransactionModalContentState();
}

class _AddTransactionModalContentState
    extends State<_AddTransactionModalContent> {
  final _formKey = GlobalKey<FormState>();
  final _amountController = TextEditingController();
  final _descriptionController = TextEditingController();

  TransactionType? _selectedType;
  DateTime _selectedDate = DateTime.now();
  bool _isLoading = false;

  @override
  void dispose() {
    _amountController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  Future<void> _selectDate() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _selectedDate,
      firstDate: DateTime(2020),
      lastDate: DateTime.now().add(const Duration(days: 1)),
      locale: const Locale('ar', 'SA'),
    );

    if (picked != null && picked != _selectedDate) {
      setState(() {
        _selectedDate = picked;
      });
    }
  }

  Future<void> _saveTransaction() async {
    if (!_formKey.currentState!.validate()) return;

    if (_selectedType == null) {
      _showErrorSnackBar('يرجى اختيار نوع المعاملة');
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      double amount = double.parse(_amountController.text.trim());

      // Adjust amount based on transaction type
      if (_selectedType == TransactionType.iOwe) {
        amount = -amount.abs(); // Make negative for amounts I owe
      } else {
        amount = amount.abs(); // Make positive for amounts owed to me
      }

      final transaction = Transaction(
        accountId: widget.account.id!,
        amount: amount,
        description:
            _descriptionController.text.trim().isEmpty
                ? null
                : _descriptionController.text.trim(),
        date: _selectedDate,
        type: _selectedType!,
      );

      // TODO: Save transaction to database
      // For now, just return the transaction

      await _showSuccessAndClose(transaction);
    } catch (e) {
      _showErrorSnackBar('خطأ في حفظ المعاملة: ${e.toString()}');
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _showSuccessAndClose(Transaction transaction) async {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('تم حفظ المعاملة بنجاح'),
        backgroundColor: Colors.green,
        duration: Duration(seconds: 2),
      ),
    );

    await Future.delayed(const Duration(milliseconds: 500));
    if (mounted) {
      Navigator.of(context).pop(transaction);
    }
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  void _closeModal() {
    Navigator.of(context).pop();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.85,
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
      ),
      child: Column(
        children: [
          _buildHeader(),
          Expanded(child: _buildForm()),
          _buildActionButtons(),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          Container(
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: Colors.grey[300],
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Icon(
                Icons.add_circle_outline,
                color: Theme.of(context).colorScheme.primary,
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'إضافة معاملة جديدة',
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Text(
                      'حساب: ${widget.account.name}',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Theme.of(
                          context,
                        ).colorScheme.onSurface.withValues(alpha: 0.7),
                      ),
                    ),
                  ],
                ),
              ),
              IconButton(onPressed: _closeModal, icon: const Icon(Icons.close)),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildForm() {
    return Form(
      key: _formKey,
      child: ListView(
        padding: const EdgeInsets.all(16),
        children: [
          // Transaction Type Selection
          Text(
            'نوع المعاملة',
            style: Theme.of(
              context,
            ).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w600),
          ),
          const SizedBox(height: 8),
          _buildTransactionTypeSelector(),
          const SizedBox(height: 16),

          // Amount Field
          CustomTextField(
            label: 'المبلغ',
            hint: '0.00',
            controller: _amountController,
            validator: Validators.validateAmount,
            keyboardType: const TextInputType.numberWithOptions(decimal: true),
            inputFormatters: [AmountInputFormatter()],
            prefixIcon: const Icon(Icons.attach_money),
            suffixIcon: Padding(
              padding: const EdgeInsets.all(12),
              child: Text(
                widget.currency.symbol,
                style: const TextStyle(fontWeight: FontWeight.bold),
              ),
            ),
          ),
          const SizedBox(height: 16),

          // Date Field
          CustomTextField(
            label: 'تاريخ المعاملة',
            hint: 'اختر التاريخ',
            controller: TextEditingController(
              text: DateFormatter.formatDate(_selectedDate),
            ),
            readOnly: true,
            onTap: _selectDate,
            prefixIcon: const Icon(Icons.calendar_today),
            suffixIcon: const Icon(Icons.arrow_drop_down),
          ),
          const SizedBox(height: 16),

          // Description Field
          CustomTextField(
            label: 'الوصف (اختياري)',
            hint: 'أدخل وصف المعاملة',
            controller: _descriptionController,
            maxLines: 3,
            maxLength: 200,
            textDirection: TextDirection.rtl,
            prefixIcon: const Icon(Icons.notes),
          ),
        ],
      ),
    );
  }

  Widget _buildTransactionTypeSelector() {
    return Column(
      children:
          TransactionType.values.map((type) {
            return RadioListTile<TransactionType>(
              title: Text(_getTransactionTypeArabic(type)),
              subtitle: Text(_getTransactionTypeDescription(type)),
              value: type,
              groupValue: _selectedType,
              onChanged: (TransactionType? value) {
                setState(() {
                  _selectedType = value;
                });
              },
              activeColor: Theme.of(context).colorScheme.primary,
              contentPadding: EdgeInsets.zero,
            );
          }).toList(),
    );
  }

  String _getTransactionTypeArabic(TransactionType type) {
    switch (type) {
      case TransactionType.owedToMe:
        return 'له';
      case TransactionType.iOwe:
        return 'عليه';
    }
  }

  String _getTransactionTypeDescription(TransactionType type) {
    switch (type) {
      case TransactionType.owedToMe:
        return 'مبلغ مستحق لي من العميل';
      case TransactionType.iOwe:
        return 'مبلغ مستحق عليّ للعميل';
    }
  }

  Widget _buildActionButtons() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: SafeArea(
        child: Row(
          children: [
            Expanded(
              child: OutlinedButton(
                onPressed: _isLoading ? null : _closeModal,
                child: const Text('إلغاء'),
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              flex: 2,
              child: ElevatedButton(
                onPressed: _isLoading ? null : _saveTransaction,
                child:
                    _isLoading
                        ? const SizedBox(
                          height: 20,
                          width: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(
                              Colors.white,
                            ),
                          ),
                        )
                        : const Text('حفظ المعاملة'),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
