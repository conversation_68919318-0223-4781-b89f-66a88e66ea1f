import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/report.dart';
import '../models/currency.dart';
import '../providers/currency_provider.dart';
import '../providers/category_provider.dart';
import '../utils/constants.dart';
import '../utils/performance_route_manager.dart';
import 'report_generator_screen.dart';

class ReportsScreen extends StatefulWidget {
  const ReportsScreen({super.key});

  @override
  State<ReportsScreen> createState() => _ReportsScreenState();
}

class _ReportsScreenState extends State<ReportsScreen> {
  @override
  void initState() {
    super.initState();
    _loadData();
  }

  Future<void> _loadData() async {
    final currencyProvider = Provider.of<CurrencyProvider>(context, listen: false);
    final categoryProvider = Provider.of<CategoryProvider>(context, listen: false);
    
    await Future.wait([
      currencyProvider.loadCurrencies(),
      categoryProvider.loadCategories(),
    ]);
  }

  void _navigateToReportGenerator(ReportType reportType) {
    PerformanceRouteManager.navigateTo(
      context,
      ReportGeneratorScreen(reportType: reportType),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('التقارير'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
      ),
      body: Consumer2<CurrencyProvider, CategoryProvider>(
        builder: (context, currencyProvider, categoryProvider, child) {
          if (currencyProvider.isLoading || categoryProvider.isLoading) {
            return const Center(
              child: CircularProgressIndicator(),
            );
          }

          return SingleChildScrollView(
            padding: const EdgeInsets.all(AppConstants.defaultPadding),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                // Header
                Card(
                  elevation: 2,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(AppConstants.defaultBorderRadius),
                  ),
                  child: Padding(
                    padding: const EdgeInsets.all(AppConstants.defaultPadding),
                    child: Column(
                      children: [
                        Icon(
                          Icons.assessment,
                          size: 48,
                          color: Theme.of(context).primaryColor,
                        ),
                        const SizedBox(height: AppConstants.smallPadding),
                        Text(
                          'تقارير الحسابات',
                          style: Theme.of(context).textTheme.titleLarge?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: AppConstants.smallPadding),
                        Text(
                          'اختر نوع التقرير المطلوب وقم بتصديره',
                          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            color: Colors.grey[600],
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ],
                    ),
                  ),
                ),
                const SizedBox(height: AppConstants.defaultPadding),

                // Report Types
                ...ReportType.values.map((reportType) => _buildReportCard(reportType)),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildReportCard(ReportType reportType) {
    return Card(
      elevation: 2,
      margin: const EdgeInsets.only(bottom: AppConstants.defaultPadding),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppConstants.defaultBorderRadius),
      ),
      child: InkWell(
        onTap: () => _navigateToReportGenerator(reportType),
        borderRadius: BorderRadius.circular(AppConstants.defaultBorderRadius),
        child: Padding(
          padding: const EdgeInsets.all(AppConstants.defaultPadding),
          child: Row(
            children: [
              // Icon
              Container(
                width: 56,
                height: 56,
                decoration: BoxDecoration(
                  color: _getReportColor(reportType).withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(AppConstants.smallBorderRadius),
                ),
                child: Icon(
                  _getReportIcon(reportType),
                  color: _getReportColor(reportType),
                  size: 28,
                ),
              ),
              const SizedBox(width: AppConstants.defaultPadding),
              
              // Content
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      reportType.arabicName,
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      reportType.description,
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Colors.grey[600],
                      ),
                    ),
                    const SizedBox(height: 8),
                    
                    // Requirements chips
                    Wrap(
                      spacing: 4,
                      children: [
                        if (reportType.requiresDateRange)
                          _buildRequirementChip('تحديد فترة زمنية', Icons.date_range),
                        if (reportType.requiresCategory)
                          _buildRequirementChip('تحديد فئة', Icons.category),
                        _buildRequirementChip('اختيار عملة', Icons.monetization_on),
                      ],
                    ),
                  ],
                ),
              ),
              
              // Arrow
              Icon(
                Icons.arrow_forward_ios,
                color: Colors.grey[400],
                size: 16,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildRequirementChip(String label, IconData icon) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: Colors.grey[100],
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            size: 12,
            color: Colors.grey[600],
          ),
          const SizedBox(width: 4),
          Text(
            label,
            style: TextStyle(
              fontSize: 10,
              color: Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }

  IconData _getReportIcon(ReportType reportType) {
    switch (reportType) {
      case ReportType.accountBalanceSummary:
        return Icons.account_balance_wallet;
      case ReportType.detailedAccountBalance:
        return Icons.list_alt;
      case ReportType.monthlyAccountBalance:
        return Icons.calendar_month;
      case ReportType.categoryAndCurrencySummary:
        return Icons.pie_chart;
      case ReportType.monthlyCategoryDetail:
        return Icons.timeline;
    }
  }

  Color _getReportColor(ReportType reportType) {
    switch (reportType) {
      case ReportType.accountBalanceSummary:
        return Colors.blue;
      case ReportType.detailedAccountBalance:
        return Colors.green;
      case ReportType.monthlyAccountBalance:
        return Colors.orange;
      case ReportType.categoryAndCurrencySummary:
        return Colors.purple;
      case ReportType.monthlyCategoryDetail:
        return Colors.teal;
    }
  }
}
