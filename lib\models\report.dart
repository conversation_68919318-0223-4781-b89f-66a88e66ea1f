import '../models/currency.dart';

enum ReportType {
  accountBalanceSummary,
  detailedAccountBalance,
  monthlyAccountBalance,
  categoryAndCurrencySummary,
  monthlyCategoryDetail,
}

enum ExportFormat {
  excel,
  pdf,
  whatsapp,
}

class ReportConfig {
  final ReportType type;
  final Currency? currency;
  final DateTime? startDate;
  final DateTime? endDate;
  final ExportFormat exportFormat;
  final String? categoryId;

  const ReportConfig({
    required this.type,
    this.currency,
    this.startDate,
    this.endDate,
    required this.exportFormat,
    this.categoryId,
  });

  ReportConfig copyWith({
    ReportType? type,
    Currency? currency,
    DateTime? startDate,
    DateTime? endDate,
    ExportFormat? exportFormat,
    String? categoryId,
  }) {
    return ReportConfig(
      type: type ?? this.type,
      currency: currency ?? this.currency,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      exportFormat: exportFormat ?? this.exportFormat,
      categoryId: categoryId ?? this.categoryId,
    );
  }
}

extension ReportTypeExtension on ReportType {
  String get arabicName {
    switch (this) {
      case ReportType.accountBalanceSummary:
        return 'تقرير إجمالي المبالغ للحسابات';
      case ReportType.detailedAccountBalance:
        return 'تقرير تفاصيل المبالغ للحسابات';
      case ReportType.monthlyAccountBalance:
        return 'تقرير إجمالي المبالغ شهرياً للحسابات';
      case ReportType.categoryAndCurrencySummary:
        return 'تقرير إجمالي التصنيفات والعملات';
      case ReportType.monthlyCategoryDetail:
        return 'تقرير تفصيلي للمبالغ شهرياً للتصنيف الحالي';
    }
  }

  String get description {
    switch (this) {
      case ReportType.accountBalanceSummary:
        return 'عرض إجمالي الأرصدة لجميع الحسابات';
      case ReportType.detailedAccountBalance:
        return 'عرض تفاصيل الحسابات مع المعاملات';
      case ReportType.monthlyAccountBalance:
        return 'عرض الأرصدة مجمعة شهرياً';
      case ReportType.categoryAndCurrencySummary:
        return 'تفصيل حسب الفئات والعملات';
      case ReportType.monthlyCategoryDetail:
        return 'تفاصيل شهرية للفئة الحالية';
    }
  }

  bool get requiresDateRange {
    switch (this) {
      case ReportType.monthlyAccountBalance:
      case ReportType.monthlyCategoryDetail:
        return true;
      default:
        return false;
    }
  }

  bool get requiresCategory {
    switch (this) {
      case ReportType.monthlyCategoryDetail:
        return true;
      default:
        return false;
    }
  }
}

extension ExportFormatExtension on ExportFormat {
  String get arabicName {
    switch (this) {
      case ExportFormat.excel:
        return 'إكسل';
      case ExportFormat.pdf:
        return 'PDF';
      case ExportFormat.whatsapp:
        return 'واتساب';
    }
  }

  String get fileExtension {
    switch (this) {
      case ExportFormat.excel:
        return '.xlsx';
      case ExportFormat.pdf:
        return '.pdf';
      case ExportFormat.whatsapp:
        return '.txt';
    }
  }

  String get mimeType {
    switch (this) {
      case ExportFormat.excel:
        return 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
      case ExportFormat.pdf:
        return 'application/pdf';
      case ExportFormat.whatsapp:
        return 'text/plain';
    }
  }
}

class ReportData {
  final String title;
  final String subtitle;
  final DateTime generatedAt;
  final Map<String, dynamic> data;
  final ReportConfig config;

  const ReportData({
    required this.title,
    required this.subtitle,
    required this.generatedAt,
    required this.data,
    required this.config,
  });
}

class BalanceSummary {
  final double totalOwedToMe;
  final double totalIOwe;
  final double netBalance;

  const BalanceSummary({
    required this.totalOwedToMe,
    required this.totalIOwe,
    required this.netBalance,
  });

  BalanceSummary copyWith({
    double? totalOwedToMe,
    double? totalIOwe,
    double? netBalance,
  }) {
    return BalanceSummary(
      totalOwedToMe: totalOwedToMe ?? this.totalOwedToMe,
      totalIOwe: totalIOwe ?? this.totalIOwe,
      netBalance: netBalance ?? this.netBalance,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'totalOwedToMe': totalOwedToMe,
      'totalIOwe': totalIOwe,
      'netBalance': netBalance,
    };
  }

  factory BalanceSummary.fromMap(Map<String, dynamic> map) {
    return BalanceSummary(
      totalOwedToMe: map['totalOwedToMe']?.toDouble() ?? 0.0,
      totalIOwe: map['totalIOwe']?.toDouble() ?? 0.0,
      netBalance: map['netBalance']?.toDouble() ?? 0.0,
    );
  }
}
