import 'package:flutter/material.dart';
import '../models/account.dart';
import '../utils/constants.dart';

class DebtTypeSelector extends StatelessWidget {
  final DebtType? selectedType;
  final Function(DebtType?) onChanged;
  final String? Function(DebtType?)? validator;
  final bool enabled;

  const DebtTypeSelector({
    super.key,
    this.selectedType,
    required this.onChanged,
    this.validator,
    this.enabled = true,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          AppConstants.debtType,
          style: Theme.of(context).textTheme.titleSmall?.copyWith(
            fontWeight: FontWeight.w600,
            color: Theme.of(context).colorScheme.onSurface,
          ),
        ),
        const SizedBox(height: AppConstants.smallPadding),
        FormField<DebtType>(
          initialValue: selectedType,
          validator: validator,
          builder: (FormFieldState<DebtType> field) {
            return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  decoration: BoxDecoration(
                    border: Border.all(
                      color:
                          field.hasError
                              ? Theme.of(context).colorScheme.error
                              : Theme.of(
                                context,
                              ).colorScheme.outline.withOpacity(0.5),
                    ),
                    borderRadius: BorderRadius.circular(
                      AppConstants.defaultBorderRadius,
                    ),
                  ),
                  child: Row(
                    children: [
                      Expanded(
                        child: _DebtTypeOption(
                          title: AppConstants.owedToMeLabel,
                          subtitle: 'مدين لي',
                          value: DebtType.owedToMe,
                          groupValue: field.value,
                          onChanged:
                              enabled
                                  ? (value) {
                                    field.didChange(value);
                                    onChanged(value);
                                  }
                                  : null,
                          color: Colors.green,
                          icon: Icons.trending_up,
                        ),
                      ),
                      Container(
                        width: 1,
                        height: 60,
                        color: Theme.of(
                          context,
                        ).colorScheme.outline.withOpacity(0.3),
                      ),
                      Expanded(
                        child: _DebtTypeOption(
                          title: AppConstants.iOweLabel,
                          subtitle: 'مدين له',
                          value: DebtType.iOwe,
                          groupValue: field.value,
                          onChanged:
                              enabled
                                  ? (value) {
                                    field.didChange(value);
                                    onChanged(value);
                                  }
                                  : null,
                          color: Colors.red,
                          icon: Icons.trending_down,
                        ),
                      ),
                    ],
                  ),
                ),
                if (field.hasError)
                  Padding(
                    padding: const EdgeInsets.only(
                      top: AppConstants.smallPadding,
                      left: AppConstants.defaultPadding,
                    ),
                    child: Text(
                      field.errorText!,
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Theme.of(context).colorScheme.error,
                      ),
                    ),
                  ),
              ],
            );
          },
        ),
      ],
    );
  }
}

class _DebtTypeOption extends StatelessWidget {
  final String title;
  final String subtitle;
  final DebtType value;
  final DebtType? groupValue;
  final Function(DebtType?)? onChanged;
  final Color color;
  final IconData icon;

  const _DebtTypeOption({
    required this.title,
    required this.subtitle,
    required this.value,
    this.groupValue,
    this.onChanged,
    required this.color,
    required this.icon,
  });

  @override
  Widget build(BuildContext context) {
    final isSelected = value == groupValue;
    final isEnabled = onChanged != null;

    return InkWell(
      onTap: isEnabled ? () => onChanged!(value) : null,
      borderRadius: BorderRadius.circular(AppConstants.defaultBorderRadius),
      child: Container(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Radio<DebtType>(
                  value: value,
                  groupValue: groupValue,
                  onChanged: onChanged,
                  activeColor: color,
                ),
                Icon(
                  icon,
                  color:
                      isSelected
                          ? color
                          : Theme.of(
                            context,
                          ).colorScheme.onSurface.withOpacity(0.6),
                  size: 20,
                ),
              ],
            ),
            const SizedBox(height: 4),
            Text(
              title,
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color:
                    isSelected
                        ? color
                        : Theme.of(context).colorScheme.onSurface,
              ),
            ),
            Text(
              subtitle,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color:
                    isSelected
                        ? color.withOpacity(0.8)
                        : Theme.of(
                          context,
                        ).colorScheme.onSurface.withOpacity(0.6),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class DebtTypeBadge extends StatelessWidget {
  final DebtType debtType;
  final bool compact;

  const DebtTypeBadge({
    super.key,
    required this.debtType,
    this.compact = false,
  });

  @override
  Widget build(BuildContext context) {
    final isOwedToMe = debtType == DebtType.owedToMe;
    final color = isOwedToMe ? Colors.green : Colors.red;
    final text =
        isOwedToMe ? AppConstants.owedToMeLabel : AppConstants.iOweLabel;
    final icon = isOwedToMe ? Icons.trending_up : Icons.trending_down;

    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: compact ? 4 : 8,
        vertical: compact ? 1 : 4,
      ),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(compact ? 4 : 6),
        border: Border.all(color: color.withOpacity(0.3), width: 1),
      ),
      child:
          compact
              ? Icon(icon, size: 12, color: color)
              : Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(icon, size: 14, color: color),
                  const SizedBox(width: 4),
                  Text(
                    text,
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: color,
                      fontWeight: FontWeight.w600,
                      fontSize: 12,
                    ),
                  ),
                ],
              ),
    );
  }
}
