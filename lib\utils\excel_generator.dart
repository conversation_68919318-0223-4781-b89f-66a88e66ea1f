import 'package:excel/excel.dart';
import 'package:path_provider/path_provider.dart';
import 'dart:io';
import '../models/report.dart';

class ExcelGenerator {
  static Future<String?> generateReport(ReportData reportData) async {
    try {
      final excel = Excel.createExcel();
      final sheet = excel['التقرير'];

      // Remove default sheet
      excel.delete('Sheet1');

      // Set RTL direction
      sheet.isRTL = true;

      int currentRow = 0;

      // Add header
      currentRow = _addHeader(sheet, reportData, currentRow);
      currentRow += 2; // Add spacing

      // Add content based on report type
      switch (reportData.config.type) {
        case ReportType.accountBalanceSummary:
          currentRow = _addAccountBalanceSummary(sheet, reportData, currentRow);
          break;
        case ReportType.detailedAccountBalance:
          currentRow = _addDetailedAccountBalance(
            sheet,
            reportData,
            currentRow,
          );
          break;
        case ReportType.monthlyAccountBalance:
          currentRow = _addMonthlyAccountBalance(sheet, reportData, currentRow);
          break;
        case ReportType.categoryAndCurrencySummary:
          currentRow = _addCategoryAndCurrencySummary(
            sheet,
            reportData,
            currentRow,
          );
          break;
        case ReportType.monthlyCategoryDetail:
          currentRow = _addMonthlyCategoryDetail(sheet, reportData, currentRow);
          break;
      }

      // Save file
      final directory = await getApplicationDocumentsDirectory();
      final fileName = 'تقرير_${_formatDateTime(reportData.generatedAt)}.xlsx';
      final filePath = '${directory.path}/$fileName';

      final file = File(filePath);
      await file.writeAsBytes(excel.encode()!);

      return filePath;
    } catch (e) {
      // Error generating Excel report
      return null;
    }
  }

  static int _addHeader(Sheet sheet, ReportData reportData, int startRow) {
    int currentRow = startRow;

    // Title
    sheet.cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: currentRow))
      ..value = TextCellValue(reportData.title)
      ..cellStyle = CellStyle(
        fontSize: 18,
        bold: true,
        horizontalAlign: HorizontalAlign.Center,
      );
    currentRow++;

    // Subtitle
    sheet.cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: currentRow))
      ..value = TextCellValue(reportData.subtitle)
      ..cellStyle = CellStyle(
        fontSize: 14,
        horizontalAlign: HorizontalAlign.Center,
      );
    currentRow++;

    // Generation date
    sheet.cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: currentRow))
      ..value = TextCellValue(
        'تاريخ الإنشاء: ${_formatDateTime(reportData.generatedAt)}',
      )
      ..cellStyle = CellStyle(
        fontSize: 12,
        horizontalAlign: HorizontalAlign.Center,
      );
    currentRow++;

    // Currency info
    if (reportData.config.currency != null) {
      sheet.cell(
          CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: currentRow),
        )
        ..value = TextCellValue(
          'العملة: ${reportData.config.currency!.name} (${reportData.config.currency!.symbol})',
        )
        ..cellStyle = CellStyle(
          fontSize: 12,
          horizontalAlign: HorizontalAlign.Center,
        );
      currentRow++;
    }

    return currentRow;
  }

  static int _addAccountBalanceSummary(
    Sheet sheet,
    ReportData reportData,
    int startRow,
  ) {
    int currentRow = startRow;
    final data = reportData.data;
    final accounts = data['accounts'] as List<Map<String, dynamic>>? ?? [];
    final summary = data['summary'] as Map<String, dynamic>? ?? {};

    // Summary section
    sheet.cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: currentRow))
      ..value = TextCellValue('ملخص الأرصدة')
      ..cellStyle = CellStyle(fontSize: 16, bold: true);
    currentRow++;

    // Summary data
    final summaryItems = [
      'إجمالي المبالغ لي: ${_formatNumber(summary['totalOwedToMe'] ?? 0)}',
      'إجمالي المبالغ علي: ${_formatNumber(summary['totalIOwe'] ?? 0)}',
      'الصافي: ${_formatNumber(summary['netBalance'] ?? 0)}',
      'عدد الحسابات: ${summary['accountCount'] ?? 0}',
    ];

    for (final item in summaryItems) {
      sheet.cell(
          CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: currentRow),
        )
        ..value = TextCellValue(item)
        ..cellStyle = CellStyle(fontSize: 12);
      currentRow++;
    }

    currentRow += 2; // Add spacing

    // Accounts table
    if (accounts.isNotEmpty) {
      sheet.cell(
          CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: currentRow),
        )
        ..value = TextCellValue('تفاصيل الحسابات')
        ..cellStyle = CellStyle(fontSize: 16, bold: true);
      currentRow++;

      // Table headers
      final headers = ['الاسم', 'رقم الهاتف', 'الرصيد', 'النوع'];
      for (int i = 0; i < headers.length; i++) {
        sheet.cell(
            CellIndex.indexByColumnRow(columnIndex: i, rowIndex: currentRow),
          )
          ..value = TextCellValue(headers[i])
          ..cellStyle = CellStyle(fontSize: 12, bold: true);
      }
      currentRow++;

      // Table data
      for (final account in accounts) {
        final rowData = [
          account['name'] ?? '',
          account['phone'] ?? '',
          _formatNumber(account['balance'] ?? 0),
          _getDebtTypeText(account['debtType'] ?? 0),
        ];

        for (int i = 0; i < rowData.length; i++) {
          sheet.cell(
              CellIndex.indexByColumnRow(columnIndex: i, rowIndex: currentRow),
            )
            ..value = TextCellValue(rowData[i])
            ..cellStyle = CellStyle(fontSize: 11);
        }
        currentRow++;
      }
    }

    return currentRow;
  }

  static int _addDetailedAccountBalance(
    Sheet sheet,
    ReportData reportData,
    int startRow,
  ) {
    int currentRow = startRow;
    final data = reportData.data;
    final accountDetails =
        data['accountDetails'] as List<Map<String, dynamic>>? ?? [];

    sheet.cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: currentRow))
      ..value = TextCellValue('تفاصيل الحسابات')
      ..cellStyle = CellStyle(fontSize: 16, bold: true);
    currentRow++;

    // Headers
    final headers = [
      'الاسم',
      'رقم الهاتف',
      'العنوان',
      'الرصيد',
      'النوع',
      'تاريخ الإنشاء',
    ];
    for (int i = 0; i < headers.length; i++) {
      sheet.cell(
          CellIndex.indexByColumnRow(columnIndex: i, rowIndex: currentRow),
        )
        ..value = TextCellValue(headers[i])
        ..cellStyle = CellStyle(fontSize: 12, bold: true);
    }
    currentRow++;

    // Data
    for (final detail in accountDetails) {
      final account = detail['account'] as Map<String, dynamic>;
      final balance = detail['balance'] ?? 0;

      final rowData = [
        account['name'] ?? '',
        account['phoneNumber'] ?? '',
        account['address'] ?? '',
        _formatNumber(balance),
        _getDebtTypeText(account['debtType'] ?? 0),
        _formatDate(
          DateTime.fromMillisecondsSinceEpoch(account['createdAt'] ?? 0),
        ),
      ];

      for (int i = 0; i < rowData.length; i++) {
        sheet.cell(
            CellIndex.indexByColumnRow(columnIndex: i, rowIndex: currentRow),
          )
          ..value = TextCellValue(rowData[i])
          ..cellStyle = CellStyle(fontSize: 11);
      }
      currentRow++;
    }

    return currentRow;
  }

  static int _addMonthlyAccountBalance(
    Sheet sheet,
    ReportData reportData,
    int startRow,
  ) {
    int currentRow = startRow;
    final data = reportData.data;
    final monthlyData = data['monthlyData'] as Map<String, dynamic>? ?? {};

    sheet.cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: currentRow))
      ..value = TextCellValue('الأرصدة الشهرية')
      ..cellStyle = CellStyle(fontSize: 16, bold: true);
    currentRow++;

    // Headers
    final headers = [
      'الشهر',
      'إجمالي لي',
      'إجمالي علي',
      'الصافي',
      'عدد الحسابات',
    ];
    for (int i = 0; i < headers.length; i++) {
      sheet.cell(
          CellIndex.indexByColumnRow(columnIndex: i, rowIndex: currentRow),
        )
        ..value = TextCellValue(headers[i])
        ..cellStyle = CellStyle(fontSize: 12, bold: true);
    }
    currentRow++;

    // Data
    for (final entry in monthlyData.entries) {
      final monthData = entry.value as Map<String, dynamic>;
      final rowData = [
        entry.key,
        _formatNumber(monthData['totalOwedToMe'] ?? 0),
        _formatNumber(monthData['totalIOwe'] ?? 0),
        _formatNumber(monthData['netBalance'] ?? 0),
        monthData['accountCount']?.toString() ?? '0',
      ];

      for (int i = 0; i < rowData.length; i++) {
        sheet.cell(
            CellIndex.indexByColumnRow(columnIndex: i, rowIndex: currentRow),
          )
          ..value = TextCellValue(rowData[i])
          ..cellStyle = CellStyle(fontSize: 11);
      }
      currentRow++;
    }

    return currentRow;
  }

  static int _addCategoryAndCurrencySummary(
    Sheet sheet,
    ReportData reportData,
    int startRow,
  ) {
    int currentRow = startRow;

    // Category summary
    sheet.cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: currentRow))
      ..value = TextCellValue('ملخص الفئات')
      ..cellStyle = CellStyle(fontSize: 16, bold: true);
    currentRow += 2;

    // Currency summary
    sheet.cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: currentRow))
      ..value = TextCellValue('ملخص العملات')
      ..cellStyle = CellStyle(fontSize: 16, bold: true);
    currentRow++;

    return currentRow;
  }

  static int _addMonthlyCategoryDetail(
    Sheet sheet,
    ReportData reportData,
    int startRow,
  ) {
    return _addMonthlyAccountBalance(sheet, reportData, startRow);
  }

  static String _formatDateTime(DateTime dateTime) {
    return '${dateTime.day}/${dateTime.month}/${dateTime.year} ${dateTime.hour}:${dateTime.minute.toString().padLeft(2, '0')}';
  }

  static String _formatDate(DateTime dateTime) {
    return '${dateTime.day}/${dateTime.month}/${dateTime.year}';
  }

  static String _formatNumber(double number) {
    return number.toStringAsFixed(2);
  }

  static String _getDebtTypeText(int debtType) {
    switch (debtType) {
      case 0:
        return 'له';
      case 1:
        return 'عليه';
      default:
        return 'غير محدد';
    }
  }
}
