import 'package:path_provider/path_provider.dart';
import 'dart:io';
import 'dart:convert';
import '../models/report.dart';

class WhatsAppGenerator {
  static Future<String?> generateReport(ReportData reportData) async {
    try {
      final content = _generateTextContent(reportData);

      // Save file
      final directory = await getApplicationDocumentsDirectory();
      final fileName = 'تقرير_${_formatDateTime(reportData.generatedAt)}.txt';
      final filePath = '${directory.path}/$fileName';

      final file = File(filePath);
      await file.writeAsString(content, encoding: utf8);

      return filePath;
    } catch (e) {
      print('Error generating WhatsApp report: $e');
      return null;
    }
  }

  static String _generateTextContent(ReportData reportData) {
    final buffer = StringBuffer();

    // Header
    buffer.writeln('📊 ${reportData.title}');
    buffer.writeln('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
    buffer.writeln('📝 ${reportData.subtitle}');
    buffer.writeln(
      '📅 تاريخ الإنشاء: ${_formatDateTime(reportData.generatedAt)}',
    );

    if (reportData.config.currency != null) {
      buffer.writeln(
        '💰 العملة: ${reportData.config.currency!.name} (${reportData.config.currency!.symbol})',
      );
    }

    buffer.writeln('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
    buffer.writeln();

    // Content based on report type
    switch (reportData.config.type) {
      case ReportType.accountBalanceSummary:
        _addAccountBalanceSummary(buffer, reportData);
        break;
      case ReportType.detailedAccountBalance:
        _addDetailedAccountBalance(buffer, reportData);
        break;
      case ReportType.monthlyAccountBalance:
        _addMonthlyAccountBalance(buffer, reportData);
        break;
      case ReportType.categoryAndCurrencySummary:
        _addCategoryAndCurrencySummary(buffer, reportData);
        break;
      case ReportType.monthlyCategoryDetail:
        _addMonthlyCategoryDetail(buffer, reportData);
        break;
    }

    // Footer
    buffer.writeln();
    buffer.writeln('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
    buffer.writeln('📱 تم إنشاء هذا التقرير بواسطة تطبيق دفتر الحسابات');

    return buffer.toString();
  }

  static void _addAccountBalanceSummary(
    StringBuffer buffer,
    ReportData reportData,
  ) {
    final data = reportData.data;
    final accounts = data['accounts'] as List<Map<String, dynamic>>? ?? [];
    final summary = data['summary'] as Map<String, dynamic>? ?? {};

    // Summary section
    buffer.writeln('📈 ملخص الأرصدة:');
    buffer.writeln('┌─────────────────────────────────────┐');
    buffer.writeln(
      '│ 💚 إجمالي المبالغ لي: ${_formatNumber(summary['totalOwedToMe'] ?? 0).padLeft(15)} │',
    );
    buffer.writeln(
      '│ 🔴 إجمالي المبالغ علي: ${_formatNumber(summary['totalIOwe'] ?? 0).padLeft(14)} │',
    );
    buffer.writeln(
      '│ 📊 الصافي: ${_formatNumber(summary['netBalance'] ?? 0).padLeft(22)} │',
    );
    buffer.writeln(
      '│ 👥 عدد الحسابات: ${(summary['accountCount'] ?? 0).toString().padLeft(17)} │',
    );
    buffer.writeln('└─────────────────────────────────────┘');
    buffer.writeln();

    // Accounts details
    if (accounts.isNotEmpty) {
      buffer.writeln('📋 تفاصيل الحسابات:');
      buffer.writeln();

      for (int i = 0; i < accounts.length; i++) {
        final account = accounts[i];
        final balance = account['balance'] ?? 0;
        final debtType = _getDebtTypeText(account['debtType'] ?? 0);
        final emoji =
            balance > 0
                ? '💚'
                : balance < 0
                ? '🔴'
                : '⚪';

        buffer.writeln('${i + 1}. $emoji ${account['name'] ?? 'غير محدد'}');
        buffer.writeln('   📞 ${account['phone'] ?? 'غير محدد'}');
        buffer.writeln('   💰 الرصيد: ${_formatNumber(balance)} ($debtType)');
        buffer.writeln();
      }
    }
  }

  static void _addDetailedAccountBalance(
    StringBuffer buffer,
    ReportData reportData,
  ) {
    final data = reportData.data;
    final accountDetails =
        data['accountDetails'] as List<Map<String, dynamic>>? ?? [];

    buffer.writeln('📋 تفاصيل الحسابات:');
    buffer.writeln();

    for (int i = 0; i < accountDetails.length; i++) {
      final detail = accountDetails[i];
      final account = detail['account'] as Map<String, dynamic>;
      final balance = detail['balance'] ?? 0;
      final emoji =
          balance > 0
              ? '💚'
              : balance < 0
              ? '🔴'
              : '⚪';

      buffer.writeln('${i + 1}. $emoji ${account['name'] ?? 'غير محدد'}');
      buffer.writeln('   📞 الهاتف: ${account['phoneNumber'] ?? 'غير محدد'}');
      buffer.writeln('   📍 العنوان: ${account['address'] ?? 'غير محدد'}');
      buffer.writeln(
        '   💰 الرصيد: ${_formatNumber(balance)} (${_getDebtTypeText(account['debtType'] ?? 0)})',
      );
      buffer.writeln(
        '   📅 تاريخ الإنشاء: ${_formatDate(DateTime.fromMillisecondsSinceEpoch(account['createdAt'] ?? 0))}',
      );
      buffer.writeln();
    }
  }

  static void _addMonthlyAccountBalance(
    StringBuffer buffer,
    ReportData reportData,
  ) {
    final data = reportData.data;
    final monthlyData = data['monthlyData'] as Map<String, dynamic>? ?? {};

    buffer.writeln('📅 الأرصدة الشهرية:');
    buffer.writeln();

    if (monthlyData.isEmpty) {
      buffer.writeln('لا توجد بيانات شهرية متاحة');
      return;
    }

    buffer.writeln('┌─────────┬──────────┬──────────┬──────────┬─────────┐');
    buffer.writeln('│  الشهر   │   لي     │   علي    │  الصافي  │ الحسابات │');
    buffer.writeln('├─────────┼──────────┼──────────┼──────────┼─────────┤');

    for (final entry in monthlyData.entries) {
      final monthData = entry.value as Map<String, dynamic>;
      final month = entry.key.padRight(7);
      final owedToMe = _formatNumber(
        monthData['totalOwedToMe'] ?? 0,
      ).padLeft(8);
      final iOwe = _formatNumber(monthData['totalIOwe'] ?? 0).padLeft(8);
      final net = _formatNumber(monthData['netBalance'] ?? 0).padLeft(8);
      final count = (monthData['accountCount'] ?? 0).toString().padLeft(7);

      buffer.writeln('│ $month │ $owedToMe │ $iOwe │ $net │ $count │');
    }

    buffer.writeln('└─────────┴──────────┴──────────┴──────────┴─────────┘');
  }

  static void _addCategoryAndCurrencySummary(
    StringBuffer buffer,
    ReportData reportData,
  ) {
    final data = reportData.data;

    buffer.writeln('📊 ملخص الفئات والعملات:');
    buffer.writeln();

    final totalAccounts = data['totalAccounts'] ?? 0;
    buffer.writeln('👥 إجمالي عدد الحسابات: $totalAccounts');
    buffer.writeln();

    // Category data
    final categoryData = data['categoryData'] as Map<String, dynamic>? ?? {};
    if (categoryData.isNotEmpty) {
      buffer.writeln('📂 ملخص الفئات:');
      for (final entry in categoryData.entries) {
        final catData = entry.value as Map<String, dynamic>;
        buffer.writeln('• الفئة ${entry.key}:');
        buffer.writeln(
          '  💚 لي: ${_formatNumber(catData['totalOwedToMe'] ?? 0)}',
        );
        buffer.writeln('  🔴 علي: ${_formatNumber(catData['totalIOwe'] ?? 0)}');
        buffer.writeln('  👥 الحسابات: ${catData['accountCount'] ?? 0}');
        buffer.writeln();
      }
    }

    // Currency data
    final currencyData = data['currencyData'] as Map<String, dynamic>? ?? {};
    if (currencyData.isNotEmpty) {
      buffer.writeln('💰 ملخص العملات:');
      for (final entry in currencyData.entries) {
        final currData = entry.value as Map<String, dynamic>;
        buffer.writeln('• العملة ${entry.key}:');
        buffer.writeln(
          '  💚 لي: ${_formatNumber(currData['totalOwedToMe'] ?? 0)}',
        );
        buffer.writeln(
          '  🔴 علي: ${_formatNumber(currData['totalIOwe'] ?? 0)}',
        );
        buffer.writeln('  👥 الحسابات: ${currData['accountCount'] ?? 0}');
        buffer.writeln();
      }
    }
  }

  static void _addMonthlyCategoryDetail(
    StringBuffer buffer,
    ReportData reportData,
  ) {
    buffer.writeln('📅 التفاصيل الشهرية للفئة:');
    buffer.writeln();
    _addMonthlyAccountBalance(buffer, reportData);
  }

  static String _formatDateTime(DateTime dateTime) {
    return '${dateTime.day}/${dateTime.month}/${dateTime.year} ${dateTime.hour}:${dateTime.minute.toString().padLeft(2, '0')}';
  }

  static String _formatDate(DateTime dateTime) {
    return '${dateTime.day}/${dateTime.month}/${dateTime.year}';
  }

  static String _formatNumber(double number) {
    return number.toStringAsFixed(2);
  }

  static String _getDebtTypeText(int debtType) {
    switch (debtType) {
      case 0:
        return 'له';
      case 1:
        return 'عليه';
      default:
        return 'غير محدد';
    }
  }
}
