class PersonalInfo {
  final int? id;
  final String fullName;
  final String mobileNumber;
  final String address;
  final String emailAddress;
  final String? logoPath;
  final DateTime createdAt;
  final DateTime updatedAt;

  const PersonalInfo({
    this.id,
    required this.fullName,
    required this.mobileNumber,
    required this.address,
    required this.emailAddress,
    this.logoPath,
    required this.createdAt,
    required this.updatedAt,
  });

  PersonalInfo copyWith({
    int? id,
    String? fullName,
    String? mobileNumber,
    String? address,
    String? emailAddress,
    String? logoPath,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return PersonalInfo(
      id: id ?? this.id,
      fullName: fullName ?? this.fullName,
      mobileNumber: mobileNumber ?? this.mobileNumber,
      address: address ?? this.address,
      emailAddress: emailAddress ?? this.emailAddress,
      logoPath: logoPath ?? this.logoPath,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'full_name': fullName,
      'mobile_number': mobileNumber,
      'address': address,
      'email_address': emailAddress,
      'logo_path': logoPath,
      'created_at': createdAt.millisecondsSinceEpoch,
      'updated_at': updatedAt.millisecondsSinceEpoch,
    };
  }

  factory PersonalInfo.fromMap(Map<String, dynamic> map) {
    return PersonalInfo(
      id: map['id']?.toInt(),
      fullName: map['full_name'] ?? '',
      mobileNumber: map['mobile_number'] ?? '',
      address: map['address'] ?? '',
      emailAddress: map['email_address'] ?? '',
      logoPath: map['logo_path'],
      createdAt: DateTime.fromMillisecondsSinceEpoch(map['created_at']),
      updatedAt: DateTime.fromMillisecondsSinceEpoch(map['updated_at']),
    );
  }

  @override
  String toString() {
    return 'PersonalInfo(id: $id, fullName: $fullName, mobileNumber: $mobileNumber, address: $address, emailAddress: $emailAddress, logoPath: $logoPath, createdAt: $createdAt, updatedAt: $updatedAt)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;

    return other is PersonalInfo &&
        other.id == id &&
        other.fullName == fullName &&
        other.mobileNumber == mobileNumber &&
        other.address == address &&
        other.emailAddress == emailAddress &&
        other.logoPath == logoPath &&
        other.createdAt == createdAt &&
        other.updatedAt == updatedAt;
  }

  @override
  int get hashCode {
    return id.hashCode ^
        fullName.hashCode ^
        mobileNumber.hashCode ^
        address.hashCode ^
        emailAddress.hashCode ^
        logoPath.hashCode ^
        createdAt.hashCode ^
        updatedAt.hashCode;
  }

  // Validation methods
  bool get isValid {
    return fullName.isNotEmpty &&
        mobileNumber.isNotEmpty &&
        address.isNotEmpty &&
        emailAddress.isNotEmpty &&
        isValidEmail(emailAddress) &&
        isValidMobileNumber(mobileNumber);
  }

  static bool isValidEmail(String email) {
    return RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(email);
  }

  static bool isValidMobileNumber(String mobile) {
    // Saudi mobile number validation (starts with 05 and 10 digits total)
    return RegExp(r'^05[0-9]{8}$').hasMatch(mobile);
  }

  String? validateFullName() {
    if (fullName.isEmpty) {
      return 'الاسم الكامل مطلوب';
    }
    if (fullName.length < 2) {
      return 'الاسم يجب أن يكون أكثر من حرفين';
    }
    return null;
  }

  String? validateMobileNumber() {
    if (mobileNumber.isEmpty) {
      return 'رقم الجوال مطلوب';
    }
    if (!isValidMobileNumber(mobileNumber)) {
      return 'رقم الجوال غير صحيح (يجب أن يبدأ بـ 05 ويكون 10 أرقام)';
    }
    return null;
  }

  String? validateAddress() {
    if (address.isEmpty) {
      return 'العنوان مطلوب';
    }
    if (address.length < 5) {
      return 'العنوان يجب أن يكون أكثر من 5 أحرف';
    }
    return null;
  }

  String? validateEmailAddress() {
    if (emailAddress.isEmpty) {
      return 'البريد الإلكتروني مطلوب';
    }
    if (!isValidEmail(emailAddress)) {
      return 'البريد الإلكتروني غير صحيح';
    }
    return null;
  }
}
