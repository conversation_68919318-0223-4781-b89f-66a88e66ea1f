import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/category.dart' as model;
import '../models/currency.dart';
import '../models/account.dart';
import '../providers/account_provider.dart';
import '../widgets/account_card.dart';
import '../widgets/account_table.dart';
import '../utils/constants.dart';
import '../widgets/simple_add_account_modal.dart';
import 'account_details_screen.dart';

class AccountListScreen extends StatefulWidget {
  final model.Category category;
  final Currency currency;

  const AccountListScreen({
    super.key,
    required this.category,
    required this.currency,
  });

  @override
  State<AccountListScreen> createState() => _AccountListScreenState();
}

class _AccountListScreenState extends State<AccountListScreen> {
  List<Account> _accounts = [];
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _loadAccounts();
  }

  Future<void> _loadAccounts() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final accountProvider = context.read<AccountProvider>();
      await accountProvider.loadAccountsByCategoryAndCurrency(
        widget.category.id!,
        widget.currency.id!,
      );

      setState(() {
        _accounts = accountProvider.accounts;
      });
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحميل الحسابات: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _navigateToAddAccount() async {
    final result = await SimpleAddAccountModal.show(context);

    if (result == true) {
      _loadAccounts();
    }
  }

  Future<void> _navigateToAccountDetails(Account account) async {
    final result = await Navigator.of(context).push(
      MaterialPageRoute(
        builder:
            (context) => AccountDetailsScreen(
              account: account,
              currency: widget.currency,
              category: widget.category,
            ),
      ),
    );

    if (result == true) {
      _loadAccounts();
    }
  }

  Future<void> _editAccount(Account account) async {
    final result = await SimpleAddAccountModal.show(
      context,
      accountToEdit: account,
    );

    if (result == true) {
      _loadAccounts();
    }
  }

  Future<void> _deleteAccount(Account account) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('تأكيد الحذف'),
            content: Text('هل أنت متأكد من حذف حساب "${account.name}"؟'),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(false),
                child: const Text(AppConstants.cancel),
              ),
              TextButton(
                onPressed: () => Navigator.of(context).pop(true),
                style: TextButton.styleFrom(
                  foregroundColor: Theme.of(context).colorScheme.error,
                ),
                child: const Text(AppConstants.delete),
              ),
            ],
          ),
    );

    if (confirmed == true && mounted) {
      final accountProvider = context.read<AccountProvider>();
      final success = await accountProvider.deleteAccount(account.id!);

      if (success) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text(AppConstants.deleteSuccess),
              backgroundColor: Colors.green,
            ),
          );
          _loadAccounts();
        }
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(accountProvider.error ?? 'فشل في حذف الحساب'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }

  double get _totalOwedToMe {
    return _accounts
        .where((account) => account.debtType == DebtType.owedToMe)
        .fold(0.0, (sum, account) => sum + account.amount);
  }

  double get _totalIOwe {
    return _accounts
        .where((account) => account.debtType == DebtType.iOwe)
        .fold(0.0, (sum, account) => sum + account.amount);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('${widget.category.name} - ${widget.currency.name}'),
        backgroundColor: Theme.of(context).colorScheme.surface,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: _navigateToAddAccount,
            tooltip: AppConstants.addAccount,
          ),
        ],
      ),
      body: Column(
        children: [
          // Summary Card
          AccountSummaryCard(
            title: '${widget.currency.name} (${widget.currency.symbol})',
            totalOwedToMe: _totalOwedToMe,
            totalIOwe: _totalIOwe,
            currency: widget.currency,
          ),

          // Accounts Table
          Expanded(
            child:
                _isLoading
                    ? const Center(child: CircularProgressIndicator())
                    : RefreshIndicator(
                      onRefresh: _loadAccounts,
                      child: SingleChildScrollView(
                        child: AccountTable(
                          accounts: _accounts,
                          currencies: {widget.currency.id!: widget.currency},
                          categories: {widget.category.id!: widget.category},
                          onAccountTap: _navigateToAccountDetails,
                          onEdit: _editAccount,
                          onDelete: _deleteAccount,
                        ),
                      ),
                    ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _navigateToAddAccount,
        tooltip: AppConstants.addAccount,
        child: const Icon(Icons.add),
      ),
    );
  }
}
