import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/category.dart' as model;
import '../models/currency.dart';
import '../models/account.dart';
import '../providers/account_provider.dart';
import '../widgets/account_card.dart';
import '../utils/constants.dart';
import '../widgets/simple_add_account_modal.dart';

class AccountListScreen extends StatefulWidget {
  final model.Category category;
  final Currency currency;

  const AccountListScreen({
    super.key,
    required this.category,
    required this.currency,
  });

  @override
  State<AccountListScreen> createState() => _AccountListScreenState();
}

class _AccountListScreenState extends State<AccountListScreen> {
  List<Account> _accounts = [];
  List<Account> _filteredAccounts = [];
  final TextEditingController _searchController = TextEditingController();
  DebtType? _filterDebtType;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _loadAccounts();
    _searchController.addListener(_filterAccounts);
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _loadAccounts() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final accountProvider = context.read<AccountProvider>();
      await accountProvider.loadAccountsByCategoryAndCurrency(
        widget.category.id!,
        widget.currency.id!,
      );

      setState(() {
        _accounts = accountProvider.accounts;
        _filteredAccounts = _accounts;
      });
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحميل الحسابات: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _filterAccounts() {
    final query = _searchController.text.toLowerCase();

    setState(() {
      _filteredAccounts =
          _accounts.where((account) {
            final matchesSearch =
                query.isEmpty ||
                account.name.toLowerCase().contains(query) ||
                account.phoneNumber.contains(query) ||
                (account.details?.toLowerCase().contains(query) ?? false);

            final matchesDebtType =
                _filterDebtType == null || account.debtType == _filterDebtType;

            return matchesSearch && matchesDebtType;
          }).toList();
    });
  }

  void _setDebtTypeFilter(DebtType? debtType) {
    setState(() {
      _filterDebtType = debtType;
    });
    _filterAccounts();
  }

  Future<void> _navigateToAddAccount() async {
    final result = await SimpleAddAccountModal.show(context);

    if (result == true) {
      _loadAccounts();
    }
  }

  Future<void> _editAccount(Account account) async {
    final result = await SimpleAddAccountModal.show(
      context,
      accountToEdit: account,
    );

    if (result == true) {
      _loadAccounts();
    }
  }

  Future<void> _deleteAccount(Account account) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('تأكيد الحذف'),
            content: Text('هل أنت متأكد من حذف حساب "${account.name}"؟'),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(false),
                child: const Text(AppConstants.cancel),
              ),
              TextButton(
                onPressed: () => Navigator.of(context).pop(true),
                style: TextButton.styleFrom(
                  foregroundColor: Theme.of(context).colorScheme.error,
                ),
                child: const Text(AppConstants.delete),
              ),
            ],
          ),
    );

    if (confirmed == true && mounted) {
      final accountProvider = context.read<AccountProvider>();
      final success = await accountProvider.deleteAccount(account.id!);

      if (success) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text(AppConstants.deleteSuccess),
              backgroundColor: Colors.green,
            ),
          );
          _loadAccounts();
        }
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(accountProvider.error ?? 'فشل في حذف الحساب'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }

  double get _totalOwedToMe {
    return _filteredAccounts
        .where((account) => account.debtType == DebtType.owedToMe)
        .fold(0.0, (sum, account) => sum + account.amount);
  }

  double get _totalIOwe {
    return _filteredAccounts
        .where((account) => account.debtType == DebtType.iOwe)
        .fold(0.0, (sum, account) => sum + account.amount);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('${widget.category.name} - ${widget.currency.name}'),
        backgroundColor: Theme.of(context).colorScheme.surface,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: _navigateToAddAccount,
            tooltip: AppConstants.addAccount,
          ),
        ],
      ),
      body: Column(
        children: [
          // Summary Card
          AccountSummaryCard(
            title: '${widget.currency.name} (${widget.currency.symbol})',
            totalOwedToMe: _totalOwedToMe,
            totalIOwe: _totalIOwe,
            currency: widget.currency,
          ),

          // Search and Filter
          Padding(
            padding: const EdgeInsets.all(AppConstants.defaultPadding),
            child: Column(
              children: [
                // Search Bar
                TextField(
                  controller: _searchController,
                  decoration: InputDecoration(
                    hintText: 'البحث في الحسابات...',
                    prefixIcon: const Icon(Icons.search),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(
                        AppConstants.defaultBorderRadius,
                      ),
                    ),
                    filled: true,
                    fillColor: Theme.of(context).colorScheme.surface,
                  ),
                ),
                const SizedBox(height: AppConstants.smallPadding),

                // Filter Chips
                Row(
                  children: [
                    Text(
                      'تصفية:',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(width: AppConstants.smallPadding),
                    Expanded(
                      child: Wrap(
                        spacing: AppConstants.smallPadding,
                        children: [
                          FilterChip(
                            label: const Text('الكل'),
                            selected: _filterDebtType == null,
                            onSelected: (selected) {
                              if (selected) _setDebtTypeFilter(null);
                            },
                          ),
                          FilterChip(
                            label: const Text(AppConstants.owedToMeLabel),
                            selected: _filterDebtType == DebtType.owedToMe,
                            onSelected: (selected) {
                              _setDebtTypeFilter(
                                selected ? DebtType.owedToMe : null,
                              );
                            },
                          ),
                          FilterChip(
                            label: const Text(AppConstants.iOweLabel),
                            selected: _filterDebtType == DebtType.iOwe,
                            onSelected: (selected) {
                              _setDebtTypeFilter(
                                selected ? DebtType.iOwe : null,
                              );
                            },
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),

          // Accounts List
          Expanded(
            child:
                _isLoading
                    ? const Center(child: CircularProgressIndicator())
                    : _filteredAccounts.isEmpty
                    ? Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.search_off,
                            size: 64,
                            color: Theme.of(
                              context,
                            ).colorScheme.onSurface.withValues(alpha: 0.5),
                          ),
                          const SizedBox(height: 16),
                          Text(
                            _searchController.text.isNotEmpty ||
                                    _filterDebtType != null
                                ? 'لا توجد نتائج للبحث'
                                : AppConstants.noAccounts,
                            style: Theme.of(context).textTheme.titleLarge,
                          ),
                          const SizedBox(height: 8),
                          Text(
                            _searchController.text.isNotEmpty ||
                                    _filterDebtType != null
                                ? 'جرب تغيير معايير البحث'
                                : 'قم بإضافة حساب جديد للبدء',
                            style: Theme.of(
                              context,
                            ).textTheme.bodyMedium?.copyWith(
                              color: Theme.of(
                                context,
                              ).colorScheme.onSurface.withValues(alpha: 0.7),
                            ),
                          ),
                          if (_searchController.text.isEmpty &&
                              _filterDebtType == null) ...[
                            const SizedBox(height: 16),
                            ElevatedButton.icon(
                              onPressed: _navigateToAddAccount,
                              icon: const Icon(Icons.add),
                              label: const Text(AppConstants.addAccount),
                            ),
                          ],
                        ],
                      ),
                    )
                    : RefreshIndicator(
                      onRefresh: _loadAccounts,
                      child: ListView.builder(
                        padding: const EdgeInsets.only(bottom: 80),
                        itemCount: _filteredAccounts.length,
                        itemBuilder: (context, index) {
                          final account = _filteredAccounts[index];
                          return AccountCard(
                            account: account,
                            currency: widget.currency,
                            onEdit: () => _editAccount(account),
                            onDelete: () => _deleteAccount(account),
                          );
                        },
                      ),
                    ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _navigateToAddAccount,
        child: const Icon(Icons.add),
        tooltip: AppConstants.addAccount,
      ),
    );
  }
}
