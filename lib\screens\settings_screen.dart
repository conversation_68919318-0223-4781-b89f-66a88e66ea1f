import 'package:flutter/material.dart';
import '../utils/constants.dart';
import 'category_management_screen.dart';
import 'currency_management_screen.dart';
import 'personal_info_screen.dart';
import 'reports_screen.dart';

class SettingsScreen extends StatelessWidget {
  const SettingsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(AppConstants.settings),
        backgroundColor: Theme.of(context).colorScheme.surface,
        elevation: 0,
      ),
      body: ListView(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        children: [
          // App Info Card
          Card(
            elevation: 2,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(
                AppConstants.defaultBorderRadius,
              ),
            ),
            child: Padding(
              padding: const EdgeInsets.all(AppConstants.defaultPadding),
              child: Column(
                children: [
                  Icon(
                    Icons.account_balance_wallet,
                    size: 48,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                  const SizedBox(height: AppConstants.smallPadding),
                  Text(
                    AppConstants.appName,
                    style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Text(
                    'الإصدار ${AppConstants.appVersion}',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Theme.of(
                        context,
                      ).colorScheme.onSurface.withOpacity(0.7),
                    ),
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: AppConstants.defaultPadding),

          // Management Section
          Text(
            'إدارة البيانات',
            style: Theme.of(
              context,
            ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: AppConstants.smallPadding),

          // Categories Management
          _SettingsCard(
            icon: Icons.category,
            title: 'إدارة الفئات',
            subtitle: 'إضافة وتعديل وحذف الفئات',
            onTap: () async {
              final result = await Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) => const CategoryManagementScreen(),
                ),
              );
              if (result == true && context.mounted) {
                Navigator.of(context).pop(true);
              }
            },
          ),
          const SizedBox(height: AppConstants.smallPadding),

          // Currencies Management
          _SettingsCard(
            icon: Icons.monetization_on,
            title: 'إدارة العملات',
            subtitle: 'إضافة وتعديل وحذف العملات',
            onTap: () async {
              final result = await Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) => const CurrencyManagementScreen(),
                ),
              );
              if (result == true && context.mounted) {
                Navigator.of(context).pop(true);
              }
            },
          ),
          const SizedBox(height: AppConstants.smallPadding),

          // Personal Information
          _SettingsCard(
            icon: Icons.person,
            title: 'البيانات الشخصية',
            subtitle: 'إدارة المعلومات الشخصية والشعار',
            onTap: () async {
              await Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) => const PersonalInfoScreen(),
                ),
              );
            },
          ),
          const SizedBox(height: AppConstants.smallPadding),

          // Reports
          _SettingsCard(
            icon: Icons.assessment,
            title: 'التقارير',
            subtitle: 'إنشاء وتصدير التقارير المالية',
            onTap: () async {
              await Navigator.of(context).push(
                MaterialPageRoute(builder: (context) => const ReportsScreen()),
              );
            },
          ),
          const SizedBox(height: AppConstants.largePadding),

          // About Section
          Text(
            'حول التطبيق',
            style: Theme.of(
              context,
            ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: AppConstants.smallPadding),

          _SettingsCard(
            icon: Icons.info_outline,
            title: 'معلومات التطبيق',
            subtitle: 'تطبيق إدارة الديون والحسابات',
            onTap: () {
              showAboutDialog(
                context: context,
                applicationName: AppConstants.appName,
                applicationVersion: AppConstants.appVersion,
                applicationIcon: Icon(
                  Icons.account_balance_wallet,
                  size: 48,
                  color: Theme.of(context).colorScheme.primary,
                ),
                children: [
                  const Text(
                    'تطبيق شامل لإدارة الديون والحسابات المالية مع دعم العملات المتعددة والفئات المخصصة.',
                  ),
                ],
              );
            },
          ),
          const SizedBox(height: AppConstants.smallPadding),

          _SettingsCard(
            icon: Icons.help_outline,
            title: 'المساعدة',
            subtitle: 'كيفية استخدام التطبيق',
            onTap: () {
              showDialog(
                context: context,
                builder:
                    (context) => AlertDialog(
                      title: const Text('كيفية الاستخدام'),
                      content: const SingleChildScrollView(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Text(
                              '1. إضافة حساب جديد:',
                              style: TextStyle(fontWeight: FontWeight.bold),
                            ),
                            Text('اضغط على زر "+" لإضافة حساب جديد'),
                            SizedBox(height: 8),
                            Text(
                              '2. تصفح الحسابات:',
                              style: TextStyle(fontWeight: FontWeight.bold),
                            ),
                            Text('استخدم التبويبات للتنقل بين الفئات المختلفة'),
                            SizedBox(height: 8),
                            Text(
                              '3. إدارة الفئات والعملات:',
                              style: TextStyle(fontWeight: FontWeight.bold),
                            ),
                            Text('اذهب إلى الإعدادات لإضافة فئات وعملات جديدة'),
                            SizedBox(height: 8),
                            Text(
                              '4. تعديل أو حذف حساب:',
                              style: TextStyle(fontWeight: FontWeight.bold),
                            ),
                            Text('اضغط على النقاط الثلاث في بطاقة الحساب'),
                          ],
                        ),
                      ),
                      actions: [
                        TextButton(
                          onPressed: () => Navigator.of(context).pop(),
                          child: const Text('فهمت'),
                        ),
                      ],
                    ),
              );
            },
          ),
        ],
      ),
    );
  }
}

class _SettingsCard extends StatelessWidget {
  final IconData icon;
  final String title;
  final String subtitle;
  final VoidCallback onTap;

  const _SettingsCard({
    required this.icon,
    required this.title,
    required this.subtitle,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 1,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppConstants.defaultBorderRadius),
      ),
      child: ListTile(
        leading: Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.primaryContainer,
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            icon,
            color: Theme.of(context).colorScheme.onPrimaryContainer,
          ),
        ),
        title: Text(
          title,
          style: Theme.of(
            context,
          ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.w600),
        ),
        subtitle: Text(subtitle),
        trailing: const Icon(Icons.arrow_forward_ios, size: 16),
        onTap: onTap,
        contentPadding: const EdgeInsets.symmetric(
          horizontal: AppConstants.defaultPadding,
          vertical: AppConstants.smallPadding,
        ),
      ),
    );
  }
}
