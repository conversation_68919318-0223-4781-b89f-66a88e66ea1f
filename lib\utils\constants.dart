import '../models/currency.dart';
import '../models/category.dart';

class AppConstants {
  // App Information
  static const String appName = 'دفتر الحسابات';
  static const String appVersion = '1.0.0';

  // Default Data
  static final List<Currency> defaultCurrencies = [
    Currency(
      name: 'ريال سعودي',
      symbol: 'ر.س',
      code: 'SAR',
      isDefault: true,
    ),
    Currency(
      name: 'دولار أمريكي',
      symbol: '\$',
      code: 'USD',
      isDefault: false,
    ),
    Currency(
      name: 'يورو',
      symbol: '€',
      code: 'EUR',
      isDefault: false,
    ),
    Currency(
      name: 'جنيه مصري',
      symbol: 'ج.م',
      code: 'EGP',
      isDefault: false,
    ),
    Currency(
      name: 'درهم إماراتي',
      symbol: 'د.إ',
      code: 'AED',
      isDefault: false,
    ),
  ];

  static final Category defaultCategory = Category(
    name: 'عام',
    description: 'الفئة العامة الافتراضية',
    isDefault: true,
  );

  // UI Constants
  static const double defaultPadding = 16.0;
  static const double smallPadding = 8.0;
  static const double largePadding = 24.0;
  
  static const double defaultBorderRadius = 12.0;
  static const double smallBorderRadius = 8.0;
  static const double largeBorderRadius = 16.0;

  // Animation Durations
  static const Duration shortAnimation = Duration(milliseconds: 200);
  static const Duration mediumAnimation = Duration(milliseconds: 300);
  static const Duration longAnimation = Duration(milliseconds: 500);

  // Form Validation
  static const int maxNameLength = 50;
  static const int maxDetailsLength = 500;
  static const int maxPhoneLength = 15;
  static const double maxAmount = 999999999.99;

  // Date Formats
  static const String dateFormat = 'yyyy/MM/dd';
  static const String dateTimeFormat = 'yyyy/MM/dd HH:mm';
  static const String arabicDateFormat = 'dd/MM/yyyy';

  // Phone Number Patterns
  static const String phonePattern = r'^[+]?[0-9]{10,15}$';
  static const String saudiPhonePattern = r'^(05|5)[0-9]{8}$';

  // Currency Formatting
  static const int currencyDecimalPlaces = 2;
  
  // Debt Type Labels
  static const String owedToMeLabel = 'له';
  static const String iOweLabel = 'عليه';
  
  // Colors (Material 3 compatible)
  static const Map<String, int> primaryColors = {
    'blue': 0xFF1976D2,
    'green': 0xFF388E3C,
    'purple': 0xFF7B1FA2,
    'orange': 0xFFE65100,
    'red': 0xFFD32F2F,
  };

  // Error Messages
  static const String networkError = 'خطأ في الاتصال';
  static const String databaseError = 'خطأ في قاعدة البيانات';
  static const String validationError = 'خطأ في التحقق من البيانات';
  static const String unknownError = 'خطأ غير معروف';

  // Success Messages
  static const String saveSuccess = 'تم الحفظ بنجاح';
  static const String updateSuccess = 'تم التحديث بنجاح';
  static const String deleteSuccess = 'تم الحذف بنجاح';

  // Confirmation Messages
  static const String deleteConfirmation = 'هل أنت متأكد من الحذف؟';
  static const String unsavedChangesConfirmation = 'لديك تغييرات غير محفوظة. هل تريد المتابعة؟';

  // Button Labels
  static const String save = 'حفظ';
  static const String cancel = 'إلغاء';
  static const String delete = 'حذف';
  static const String edit = 'تعديل';
  static const String add = 'إضافة';
  static const String confirm = 'تأكيد';
  static const String back = 'رجوع';

  // Navigation Labels
  static const String dashboard = 'الرئيسية';
  static const String settings = 'الإعدادات';
  static const String addAccount = 'إضافة حساب';
  static const String editAccount = 'تعديل حساب';
  static const String categories = 'الفئات';
  static const String currencies = 'العملات';

  // Form Labels
  static const String accountName = 'اسم الحساب';
  static const String phoneNumber = 'رقم الهاتف';
  static const String amount = 'المبلغ';
  static const String details = 'التفاصيل';
  static const String currency = 'العملة';
  static const String category = 'الفئة';
  static const String date = 'التاريخ';
  static const String debtType = 'نوع الدين';

  // Validation Messages
  static const String requiredField = 'هذا الحقل مطلوب';
  static const String invalidPhone = 'رقم هاتف غير صحيح';
  static const String invalidAmount = 'مبلغ غير صحيح';
  static const String nameTooLong = 'الاسم طويل جداً';
  static const String detailsTooLong = 'التفاصيل طويلة جداً';
  static const String amountTooLarge = 'المبلغ كبير جداً';

  // Empty States
  static const String noAccounts = 'لا توجد حسابات';
  static const String noCategories = 'لا توجد فئات';
  static const String noCurrencies = 'لا توجد عملات';
  static const String noData = 'لا توجد بيانات';

  // Loading States
  static const String loading = 'جاري التحميل...';
  static const String saving = 'جاري الحفظ...';
  static const String deleting = 'جاري الحذف...';
}
