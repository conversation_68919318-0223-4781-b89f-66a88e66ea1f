import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/account.dart';
import '../models/currency.dart';
import '../models/category.dart' as model;
import '../providers/account_provider.dart';
import '../providers/currency_provider.dart';
import '../providers/category_provider.dart';
import '../widgets/custom_text_field.dart';
import '../widgets/debt_type_selector.dart';
import '../utils/constants.dart';
import '../utils/validators.dart';
import '../utils/formatters.dart';

class AddAccountModal {
  static Future<bool?> show(BuildContext context, {Account? accountToEdit}) {
    return showModalBottomSheet<bool>(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      enableDrag: true,
      isDismissible: true,
      builder:
          (context) => _AddAccountModalContent(accountToEdit: accountToEdit),
    );
  }
}

class _AddAccountModalContent extends StatefulWidget {
  final Account? accountToEdit;

  const _AddAccountModalContent({this.accountToEdit});

  @override
  State<_AddAccountModalContent> createState() =>
      _AddAccountModalContentState();
}

class _AddAccountModalContentState extends State<_AddAccountModalContent>
    with TickerProviderStateMixin {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _phoneController = TextEditingController();
  final _amountController = TextEditingController();
  final _detailsController = TextEditingController();

  Currency? _selectedCurrency;
  model.Category? _selectedCategory;
  DebtType? _selectedDebtType;
  DateTime _selectedDate = DateTime.now();
  bool _isLoading = false;

  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _opacityAnimation;

  bool get isEditing => widget.accountToEdit != null;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _initializeForm();
  }

  void _initializeAnimations() {
    _animationController = AnimationController(
      duration: AppConstants.mediumAnimation,
      vsync: this,
    );

    _scaleAnimation = Tween<double>(begin: 0.8, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeOutBack),
    );

    _opacityAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeOut),
    );

    _animationController.forward();
  }

  void _initializeForm() {
    if (isEditing) {
      final account = widget.accountToEdit!;
      _nameController.text = account.name;
      _phoneController.text = account.phoneNumber;
      _amountController.text = account.amount.toString();
      _detailsController.text = account.details ?? '';
      _selectedDebtType = account.debtType;
      _selectedDate = account.date;

      WidgetsBinding.instance.addPostFrameCallback((_) {
        final currencyProvider = context.read<CurrencyProvider>();
        final categoryProvider = context.read<CategoryProvider>();

        setState(() {
          _selectedCurrency = currencyProvider.getCurrencyById(
            account.currencyId,
          );
          _selectedCategory = categoryProvider.getCategoryById(
            account.categoryId,
          );
        });
      });
    } else {
      // Set default values for new account
      WidgetsBinding.instance.addPostFrameCallback((_) {
        final currencyProvider = context.read<CurrencyProvider>();
        final categoryProvider = context.read<CategoryProvider>();

        setState(() {
          _selectedCurrency = currencyProvider.defaultCurrency;
          _selectedCategory = categoryProvider.defaultCategory;
        });
      });
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    _nameController.dispose();
    _phoneController.dispose();
    _amountController.dispose();
    _detailsController.dispose();
    super.dispose();
  }

  Future<void> _selectDate() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _selectedDate,
      firstDate: DateTime(2020),
      lastDate: DateTime.now().add(const Duration(days: 1)),
      locale: const Locale('ar', 'SA'),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: Theme.of(context).colorScheme.copyWith(
              primary: Theme.of(context).colorScheme.primary,
            ),
          ),
          child: child!,
        );
      },
    );

    if (picked != null && picked != _selectedDate) {
      setState(() {
        _selectedDate = picked;
      });
    }
  }

  Future<void> _saveAccount() async {
    if (!_formKey.currentState!.validate()) return;
    // Check required fields
    if (_selectedCurrency == null) {
      _showErrorSnackBar('يرجى اختيار العملة');
      return;
    }
    if (_selectedCategory == null) {
      _showErrorSnackBar('يرجى اختيار الفئة');
      return;
    }
    if (_selectedDebtType == null) {
      _showErrorSnackBar('يرجى اختيار نوع الدين');
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final accountProvider = context.read<AccountProvider>();

      final account = Account(
        id: isEditing ? widget.accountToEdit!.id : null,
        name: _nameController.text.trim(),
        phoneNumber: _phoneController.text.trim(),
        amount: double.parse(_amountController.text.trim()),
        details:
            _detailsController.text.trim().isEmpty
                ? null
                : _detailsController.text.trim(),
        currencyId: _selectedCurrency!.id!,
        categoryId: _selectedCategory!.id!,
        date: _selectedDate,
        debtType: _selectedDebtType!,
        createdAt: isEditing ? widget.accountToEdit!.createdAt : DateTime.now(),
      );

      bool success;
      if (isEditing) {
        success = await accountProvider.updateAccount(account);
      } else {
        success = await accountProvider.addAccount(account);
      }

      if (success) {
        await _showSuccessAndClose();
      } else {
        _showErrorSnackBar(accountProvider.error ?? 'حدث خطأ غير متوقع');
      }
    } catch (e) {
      _showErrorSnackBar('خطأ: ${e.toString()}');
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _showSuccessAndClose() async {
    // Show success message
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.check_circle, color: Colors.white),
            const SizedBox(width: 8),
            Text(
              isEditing ? AppConstants.updateSuccess : AppConstants.saveSuccess,
            ),
          ],
        ),
        backgroundColor: Colors.green,
        duration: const Duration(seconds: 2),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      ),
    );

    // Wait a bit then close
    await Future.delayed(const Duration(milliseconds: 500));
    if (mounted) {
      Navigator.of(context).pop(true);
    }
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.error, color: Colors.white),
            const SizedBox(width: 8),
            Expanded(child: Text(message)),
          ],
        ),
        backgroundColor: Colors.red,
        duration: const Duration(seconds: 3),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      ),
    );
  }

  void _closeModal() {
    Navigator.of(context).pop(false);
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Container(
          decoration: BoxDecoration(
            color: Colors.black.withValues(
              alpha: 0.3 * _opacityAnimation.value,
            ),
          ),
          child: DraggableScrollableSheet(
            initialChildSize: 0.9,
            minChildSize: 0.5,
            maxChildSize: 0.95,
            builder: (context, scrollController) {
              return Transform.scale(
                scale: _scaleAnimation.value,
                child: Container(
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.surface,
                    borderRadius: const BorderRadius.only(
                      topLeft: Radius.circular(AppConstants.largeBorderRadius),
                      topRight: Radius.circular(AppConstants.largeBorderRadius),
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.1),
                        blurRadius: 10,
                        offset: const Offset(0, -2),
                      ),
                    ],
                  ),
                  child: Column(
                    children: [
                      _buildHeader(),
                      Expanded(child: _buildForm(scrollController)),
                      _buildActionButtons(),
                    ],
                  ),
                ),
              );
            },
          ),
        );
      },
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      decoration: BoxDecoration(
        color: Theme.of(
          context,
        ).colorScheme.primaryContainer.withValues(alpha: 0.3),
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(AppConstants.largeBorderRadius),
          topRight: Radius.circular(AppConstants.largeBorderRadius),
        ),
      ),
      child: Column(
        children: [
          // Drag handle
          Container(
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: Theme.of(
                context,
              ).colorScheme.onSurface.withValues(alpha: 0.3),
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          const SizedBox(height: AppConstants.defaultPadding),

          // Header content
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Theme.of(
                    context,
                  ).colorScheme.primary.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  isEditing ? Icons.edit : Icons.add,
                  color: Theme.of(context).colorScheme.primary,
                  size: 24,
                ),
              ),
              const SizedBox(width: AppConstants.defaultPadding),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      isEditing
                          ? AppConstants.editAccount
                          : AppConstants.addAccount,
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Text(
                      isEditing
                          ? 'تعديل بيانات الحساب'
                          : 'إضافة حساب جديد إلى دفتر الحسابات',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Theme.of(
                          context,
                        ).colorScheme.onSurface.withValues(alpha: 0.7),
                      ),
                    ),
                  ],
                ),
              ),
              IconButton(
                onPressed: _closeModal,
                icon: const Icon(Icons.close),
                tooltip: 'إغلاق',
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildForm(ScrollController scrollController) {
    return Form(
      key: _formKey,
      child: ListView(
        controller: scrollController,
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        children: [
          _buildAccountInfoSection(),
          const SizedBox(height: AppConstants.largePadding),
          _buildAmountSection(),
          const SizedBox(height: AppConstants.largePadding),
          _buildDetailsSection(),
          const SizedBox(height: AppConstants.largePadding * 2),
        ],
      ),
    );
  }

  Widget _buildSectionCard({
    required String title,
    required IconData icon,
    required List<Widget> children,
  }) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppConstants.defaultBorderRadius),
      ),
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(6),
                  decoration: BoxDecoration(
                    color: Theme.of(
                      context,
                    ).colorScheme.primary.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(6),
                  ),
                  child: Icon(
                    icon,
                    size: 18,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                ),
                const SizedBox(width: AppConstants.smallPadding),
                Text(
                  title,
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                ),
              ],
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            ...children,
          ],
        ),
      ),
    );
  }

  Widget _buildAccountInfoSection() {
    return _buildSectionCard(
      title: 'معلومات الحساب',
      icon: Icons.person,
      children: [
        CustomTextField(
          label: AppConstants.accountName,
          hint: 'أدخل اسم الحساب',
          controller: _nameController,
          validator: Validators.validateName,
          keyboardType: TextInputType.name,
          textDirection: TextDirection.rtl,
          prefixIcon: const Icon(Icons.person_outline),
        ),
        const SizedBox(height: AppConstants.defaultPadding),

        CustomTextField(
          label: AppConstants.phoneNumber,
          hint: '05xxxxxxxx',
          controller: _phoneController,
          validator: Validators.validatePhoneNumber,
          keyboardType: TextInputType.phone,
          inputFormatters: [PhoneNumberFormatter()],
          prefixIcon: const Icon(Icons.phone),
        ),
        const SizedBox(height: AppConstants.defaultPadding),

        Consumer<CategoryProvider>(
          builder: (context, categoryProvider, child) {
            if (categoryProvider.categories.isEmpty) {
              return Container(
                padding: const EdgeInsets.all(AppConstants.defaultPadding),
                decoration: BoxDecoration(
                  color: Theme.of(
                    context,
                  ).colorScheme.errorContainer.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(
                    AppConstants.defaultBorderRadius,
                  ),
                  border: Border.all(
                    color: Theme.of(
                      context,
                    ).colorScheme.error.withValues(alpha: 0.3),
                  ),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.warning,
                      color: Theme.of(context).colorScheme.error,
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        'لا توجد فئات متاحة. يرجى إنشاء فئة من الإعدادات أولاً.',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Theme.of(context).colorScheme.error,
                        ),
                      ),
                    ),
                  ],
                ),
              );
            }

            if (_selectedCategory == null &&
                categoryProvider.categories.isNotEmpty) {
              WidgetsBinding.instance.addPostFrameCallback((_) {
                if (mounted) {
                  setState(() {
                    _selectedCategory = categoryProvider.defaultCategory;
                  });
                }
              });
            }

            return CustomDropdownField<model.Category>(
              label: AppConstants.category,
              hint: 'اختر الفئة',
              value: _selectedCategory,
              items:
                  categoryProvider.categories.map((category) {
                    return DropdownMenuItem<model.Category>(
                      value: category,
                      child: Row(
                        children: [
                          Icon(
                            category.isDefault ? Icons.home : Icons.folder,
                            size: 16,
                            color: Theme.of(
                              context,
                            ).colorScheme.onSurface.withValues(alpha: 0.6),
                          ),
                          const SizedBox(width: 8),
                          Text(category.name),
                        ],
                      ),
                    );
                  }).toList(),
              onChanged: (category) {
                setState(() {
                  _selectedCategory = category;
                });
              },
              validator:
                  (value) =>
                      Validators.validateRequired(value, AppConstants.category),
              prefixIcon: const Icon(Icons.category),
            );
          },
        ),
      ],
    );
  }

  Widget _buildAmountSection() {
    return _buildSectionCard(
      title: 'المبلغ والعملة',
      icon: Icons.monetization_on,
      children: [
        Row(
          children: [
            Expanded(
              flex: 2,
              child: CustomTextField(
                label: AppConstants.amount,
                hint: '0.00',
                controller: _amountController,
                validator: Validators.validateAmount,
                keyboardType: const TextInputType.numberWithOptions(
                  decimal: true,
                ),
                inputFormatters: [AmountInputFormatter()],
                prefixIcon: const Icon(Icons.attach_money),
              ),
            ),
            const SizedBox(width: AppConstants.defaultPadding),
            Expanded(
              flex: 1,
              child: Consumer<CurrencyProvider>(
                builder: (context, currencyProvider, child) {
                  if (currencyProvider.currencies.isEmpty) {
                    return Container(
                      padding: const EdgeInsets.all(
                        AppConstants.defaultPadding,
                      ),
                      decoration: BoxDecoration(
                        color: Theme.of(
                          context,
                        ).colorScheme.errorContainer.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(
                          AppConstants.defaultBorderRadius,
                        ),
                        border: Border.all(
                          color: Theme.of(
                            context,
                          ).colorScheme.error.withValues(alpha: 0.3),
                        ),
                      ),
                      child: Row(
                        children: [
                          Icon(
                            Icons.warning,
                            color: Theme.of(context).colorScheme.error,
                            size: 20,
                          ),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              'لا توجد عملات متاحة.',
                              style: Theme.of(
                                context,
                              ).textTheme.bodyMedium?.copyWith(
                                color: Theme.of(context).colorScheme.error,
                              ),
                            ),
                          ),
                        ],
                      ),
                    );
                  }

                  if (_selectedCurrency == null &&
                      currencyProvider.currencies.isNotEmpty) {
                    WidgetsBinding.instance.addPostFrameCallback((_) {
                      if (mounted) {
                        setState(() {
                          _selectedCurrency = currencyProvider.defaultCurrency;
                        });
                      }
                    });
                  }

                  return CustomDropdownField<Currency>(
                    label: AppConstants.currency,
                    hint: 'العملة',
                    value: _selectedCurrency,
                    items:
                        currencyProvider.currencies.map((currency) {
                          return DropdownMenuItem<Currency>(
                            value: currency,
                            child: Row(
                              children: [
                                Text(
                                  currency.symbol,
                                  style: const TextStyle(
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                const SizedBox(width: 4),
                                Expanded(
                                  child: Text(
                                    currency.name,
                                    overflow: TextOverflow.ellipsis,
                                    style: const TextStyle(fontSize: 12),
                                  ),
                                ),
                              ],
                            ),
                          );
                        }).toList(),
                    onChanged: (currency) {
                      setState(() {
                        _selectedCurrency = currency;
                      });
                    },
                    validator:
                        (value) => Validators.validateRequired(
                          value,
                          AppConstants.currency,
                        ),
                    prefixIcon: const Icon(Icons.monetization_on),
                  );
                },
              ),
            ),
          ],
        ),
        const SizedBox(height: AppConstants.defaultPadding),

        DebtTypeSelector(
          selectedType: _selectedDebtType,
          onChanged: (type) {
            setState(() {
              _selectedDebtType = type;
            });
          },
          validator:
              (value) =>
                  Validators.validateRequired(value, AppConstants.debtType),
        ),
        const SizedBox(height: AppConstants.defaultPadding),

        CustomTextField(
          label: AppConstants.date,
          hint: 'اختر التاريخ',
          controller: TextEditingController(
            text: DateFormatter.formatDate(_selectedDate),
          ),
          readOnly: true,
          onTap: _selectDate,
          prefixIcon: const Icon(Icons.calendar_today),
          suffixIcon: const Icon(Icons.arrow_drop_down),
        ),
      ],
    );
  }

  Widget _buildDetailsSection() {
    return _buildSectionCard(
      title: 'تفاصيل إضافية',
      icon: Icons.notes,
      children: [
        CustomTextField(
          label: AppConstants.details,
          hint: 'أدخل تفاصيل إضافية (اختياري)',
          controller: _detailsController,
          validator: Validators.validateDetails,
          maxLines: 3,
          maxLength: AppConstants.maxDetailsLength,
          textDirection: TextDirection.rtl,
          prefixIcon: const Icon(Icons.notes),
        ),
      ],
    );
  }

  Widget _buildActionButtons() {
    return Container(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        border: Border(
          top: BorderSide(
            color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
            width: 1,
          ),
        ),
      ),
      child: SafeArea(
        child: Row(
          children: [
            Expanded(
              child: OutlinedButton(
                onPressed: _isLoading ? null : _closeModal,
                style: OutlinedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(
                    vertical: AppConstants.defaultPadding,
                  ),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(
                      AppConstants.defaultBorderRadius,
                    ),
                  ),
                ),
                child: Text(
                  AppConstants.cancel,
                  style: Theme.of(context).textTheme.titleMedium,
                ),
              ),
            ),
            const SizedBox(width: AppConstants.defaultPadding),
            Expanded(
              flex: 2,
              child: ElevatedButton(
                onPressed: _isLoading ? null : _saveAccount,
                style: ElevatedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(
                    vertical: AppConstants.defaultPadding,
                  ),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(
                      AppConstants.defaultBorderRadius,
                    ),
                  ),
                ),
                child:
                    _isLoading
                        ? const SizedBox(
                          height: 20,
                          width: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(
                              Colors.white,
                            ),
                          ),
                        )
                        : Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(isEditing ? Icons.save : Icons.add),
                            const SizedBox(width: 8),
                            Text(
                              AppConstants.save,
                              style: Theme.of(
                                context,
                              ).textTheme.titleMedium?.copyWith(
                                color: Colors.white,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
