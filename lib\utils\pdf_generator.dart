import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:path_provider/path_provider.dart';
import 'package:flutter/foundation.dart';
import 'dart:io';
import '../models/report.dart';

class PdfGenerator {
  static Future<String?> generateReport(ReportData reportData) async {
    try {
      debugPrint('📄 Starting PDF generation for: ${reportData.title}');

      final pdf = pw.Document();

      debugPrint('📄 Adding page with RTL support...');
      // Add page with RTL support
      pdf.addPage(
        pw.MultiPage(
          pageFormat: PdfPageFormat.a4,
          textDirection: pw.TextDirection.rtl,
          build: (pw.Context context) {
            return [
              _buildHeader(reportData),
              pw.SizedBox(height: 20),
              _buildContent(reportData),
            ];
          },
        ),
      );

      debugPrint('📄 Saving PDF file...');
      // Save file
      final directory = await getApplicationDocumentsDirectory();
      final fileName = 'تقرير_${_formatDateTime(reportData.generatedAt)}.pdf';
      final filePath = '${directory.path}/$fileName';

      debugPrint('📄 File path: $filePath');

      final file = File(filePath);
      final pdfBytes = await pdf.save();
      await file.writeAsBytes(pdfBytes);

      debugPrint('✅ PDF file saved successfully');
      return filePath;
    } catch (e, stackTrace) {
      debugPrint('❌ Error generating PDF report: $e');
      debugPrint('Stack trace: $stackTrace');
      return null;
    }
  }

  static pw.Widget _buildHeader(ReportData reportData) {
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.center,
      children: [
        pw.Text(
          reportData.title,
          style: pw.TextStyle(fontSize: 24, fontWeight: pw.FontWeight.bold),
          textDirection: pw.TextDirection.rtl,
        ),
        pw.SizedBox(height: 10),
        pw.Text(
          reportData.subtitle,
          style: const pw.TextStyle(fontSize: 16),
          textDirection: pw.TextDirection.rtl,
        ),
        pw.SizedBox(height: 10),
        pw.Text(
          'تاريخ الإنشاء: ${_formatDateTime(reportData.generatedAt)}',
          style: const pw.TextStyle(fontSize: 12),
          textDirection: pw.TextDirection.rtl,
        ),
        if (reportData.config.currency != null)
          pw.Text(
            'العملة: ${reportData.config.currency!.name} (${reportData.config.currency!.symbol})',
            style: const pw.TextStyle(fontSize: 12),
            textDirection: pw.TextDirection.rtl,
          ),
        pw.Divider(thickness: 2),
      ],
    );
  }

  static pw.Widget _buildContent(ReportData reportData) {
    switch (reportData.config.type) {
      case ReportType.accountBalanceSummary:
        return _buildAccountBalanceSummary(reportData);
      case ReportType.detailedAccountBalance:
        return _buildDetailedAccountBalance(reportData);
      case ReportType.monthlyAccountBalance:
        return _buildMonthlyAccountBalance(reportData);
      case ReportType.categoryAndCurrencySummary:
        return _buildCategoryAndCurrencySummary(reportData);
      case ReportType.monthlyCategoryDetail:
        return _buildMonthlyCategoryDetail(reportData);
    }
  }

  static pw.Widget _buildAccountBalanceSummary(ReportData reportData) {
    final data = reportData.data;
    final accounts = data['accounts'] as List<Map<String, dynamic>>? ?? [];
    final summary = data['summary'] as Map<String, dynamic>? ?? {};

    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        // Summary section
        pw.Text(
          'ملخص الأرصدة',
          style: pw.TextStyle(fontSize: 18, fontWeight: pw.FontWeight.bold),
          textDirection: pw.TextDirection.rtl,
        ),
        pw.SizedBox(height: 10),

        pw.Container(
          padding: const pw.EdgeInsets.all(10),
          decoration: pw.BoxDecoration(
            border: pw.Border.all(color: PdfColors.grey),
            borderRadius: pw.BorderRadius.circular(5),
          ),
          child: pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            children: [
              _buildSummaryRow(
                'إجمالي المبالغ لي:',
                _formatNumber(summary['totalOwedToMe'] ?? 0),
              ),
              _buildSummaryRow(
                'إجمالي المبالغ علي:',
                _formatNumber(summary['totalIOwe'] ?? 0),
              ),
              _buildSummaryRow(
                'الصافي:',
                _formatNumber(summary['netBalance'] ?? 0),
              ),
              _buildSummaryRow(
                'عدد الحسابات:',
                '${summary['accountCount'] ?? 0}',
              ),
            ],
          ),
        ),

        pw.SizedBox(height: 20),

        // Accounts table
        if (accounts.isNotEmpty) ...[
          pw.Text(
            'تفاصيل الحسابات',
            style: pw.TextStyle(fontSize: 16, fontWeight: pw.FontWeight.bold),
            textDirection: pw.TextDirection.rtl,
          ),
          pw.SizedBox(height: 10),

          pw.Table(
            border: pw.TableBorder.all(color: PdfColors.grey),
            children: [
              // Header
              pw.TableRow(
                decoration: const pw.BoxDecoration(color: PdfColors.grey200),
                children: [
                  _buildTableCell('الاسم', isHeader: true),
                  _buildTableCell('رقم الهاتف', isHeader: true),
                  _buildTableCell('الرصيد', isHeader: true),
                  _buildTableCell('النوع', isHeader: true),
                ],
              ),
              // Data rows
              ...accounts.map(
                (account) => pw.TableRow(
                  children: [
                    _buildTableCell(account['name'] ?? ''),
                    _buildTableCell(account['phone'] ?? ''),
                    _buildTableCell(_formatNumber(account['balance'] ?? 0)),
                    _buildTableCell(_getDebtTypeText(account['debtType'] ?? 0)),
                  ],
                ),
              ),
            ],
          ),
        ],
      ],
    );
  }

  static pw.Widget _buildDetailedAccountBalance(ReportData reportData) {
    final data = reportData.data;
    final accountDetails =
        data['accountDetails'] as List<Map<String, dynamic>>? ?? [];

    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        pw.Text(
          'تفاصيل الحسابات',
          style: pw.TextStyle(fontSize: 18, fontWeight: pw.FontWeight.bold),
          textDirection: pw.TextDirection.rtl,
        ),
        pw.SizedBox(height: 10),

        if (accountDetails.isNotEmpty)
          pw.Table(
            border: pw.TableBorder.all(color: PdfColors.grey),
            children: [
              // Header
              pw.TableRow(
                decoration: const pw.BoxDecoration(color: PdfColors.grey200),
                children: [
                  _buildTableCell('الاسم', isHeader: true),
                  _buildTableCell('رقم الهاتف', isHeader: true),
                  _buildTableCell('العنوان', isHeader: true),
                  _buildTableCell('الرصيد', isHeader: true),
                  _buildTableCell('النوع', isHeader: true),
                ],
              ),
              // Data rows
              ...accountDetails.map((detail) {
                final account = detail['account'] as Map<String, dynamic>;
                final balance = detail['balance'] ?? 0;

                return pw.TableRow(
                  children: [
                    _buildTableCell(account['name'] ?? ''),
                    _buildTableCell(account['phoneNumber'] ?? ''),
                    _buildTableCell(account['address'] ?? ''),
                    _buildTableCell(_formatNumber(balance)),
                    _buildTableCell(_getDebtTypeText(account['debtType'] ?? 0)),
                  ],
                );
              }),
            ],
          ),
      ],
    );
  }

  static pw.Widget _buildMonthlyAccountBalance(ReportData reportData) {
    final data = reportData.data;
    final monthlyData = data['monthlyData'] as Map<String, dynamic>? ?? {};

    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        pw.Text(
          'الأرصدة الشهرية',
          style: pw.TextStyle(fontSize: 18, fontWeight: pw.FontWeight.bold),
          textDirection: pw.TextDirection.rtl,
        ),
        pw.SizedBox(height: 10),

        if (monthlyData.isNotEmpty)
          pw.Table(
            border: pw.TableBorder.all(color: PdfColors.grey),
            children: [
              // Header
              pw.TableRow(
                decoration: const pw.BoxDecoration(color: PdfColors.grey200),
                children: [
                  _buildTableCell('الشهر', isHeader: true),
                  _buildTableCell('إجمالي لي', isHeader: true),
                  _buildTableCell('إجمالي علي', isHeader: true),
                  _buildTableCell('الصافي', isHeader: true),
                  _buildTableCell('عدد الحسابات', isHeader: true),
                ],
              ),
              // Data rows
              ...monthlyData.entries.map((entry) {
                final monthData = entry.value as Map<String, dynamic>;
                return pw.TableRow(
                  children: [
                    _buildTableCell(entry.key),
                    _buildTableCell(
                      _formatNumber(monthData['totalOwedToMe'] ?? 0),
                    ),
                    _buildTableCell(_formatNumber(monthData['totalIOwe'] ?? 0)),
                    _buildTableCell(
                      _formatNumber(monthData['netBalance'] ?? 0),
                    ),
                    _buildTableCell('${monthData['accountCount'] ?? 0}'),
                  ],
                );
              }),
            ],
          ),
      ],
    );
  }

  static pw.Widget _buildCategoryAndCurrencySummary(ReportData reportData) {
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        pw.Text(
          'ملخص الفئات والعملات',
          style: pw.TextStyle(fontSize: 18, fontWeight: pw.FontWeight.bold),
          textDirection: pw.TextDirection.rtl,
        ),
        pw.SizedBox(height: 10),
        pw.Text(
          'سيتم إضافة تفاصيل الفئات والعملات هنا',
          style: const pw.TextStyle(fontSize: 12),
          textDirection: pw.TextDirection.rtl,
        ),
      ],
    );
  }

  static pw.Widget _buildMonthlyCategoryDetail(ReportData reportData) {
    return _buildMonthlyAccountBalance(reportData);
  }

  static pw.Widget _buildSummaryRow(String label, String value) {
    return pw.Padding(
      padding: const pw.EdgeInsets.symmetric(vertical: 2),
      child: pw.Row(
        mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
        children: [
          pw.Text(label, textDirection: pw.TextDirection.rtl),
          pw.Text(value, textDirection: pw.TextDirection.rtl),
        ],
      ),
    );
  }

  static pw.Widget _buildTableCell(String text, {bool isHeader = false}) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(5),
      child: pw.Text(
        text,
        style: pw.TextStyle(
          fontSize: isHeader ? 12 : 10,
          fontWeight: isHeader ? pw.FontWeight.bold : pw.FontWeight.normal,
        ),
        textDirection: pw.TextDirection.rtl,
        textAlign: pw.TextAlign.center,
      ),
    );
  }

  static String _formatDateTime(DateTime dateTime) {
    return '${dateTime.day}/${dateTime.month}/${dateTime.year} ${dateTime.hour}:${dateTime.minute.toString().padLeft(2, '0')}';
  }

  static String _formatNumber(double number) {
    return number.toStringAsFixed(2);
  }

  static String _getDebtTypeText(int debtType) {
    switch (debtType) {
      case 0:
        return 'له';
      case 1:
        return 'عليه';
      default:
        return 'غير محدد';
    }
  }
}
