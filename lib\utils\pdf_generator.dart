import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:path_provider/path_provider.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'dart:io';
import 'dart:typed_data';
import '../models/report.dart';
import '../models/account.dart';
import '../models/personal_info.dart';

class PdfGenerator {
  static pw.Font? _arabicFont;
  static pw.Font? _arabicBoldFont;

  // Load logo image from file path
  static Future<pw.ImageProvider?> _loadLogo(String? logoPath) async {
    if (logoPath == null || logoPath.isEmpty) {
      return null;
    }

    try {
      final file = File(logoPath);
      if (await file.exists()) {
        final bytes = await file.readAsBytes();
        return pw.MemoryImage(bytes);
      }
    } catch (e) {
      debugPrint('⚠️ Error loading logo: $e');
    }

    return null;
  }

  // Load Arabic fonts
  static Future<void> _loadArabicFonts() async {
    if (_arabicFont == null || _arabicBoldFont == null) {
      try {
        debugPrint('📄 Loading Arabic fonts...');

        final regularFontData = await rootBundle.load(
          'assets/fonts/NotoSansArabic-Regular.ttf',
        );
        final boldFontData = await rootBundle.load(
          'assets/fonts/NotoSansArabic-Bold.ttf',
        );

        _arabicFont = pw.Font.ttf(regularFontData);
        _arabicBoldFont = pw.Font.ttf(boldFontData);

        debugPrint('✅ Arabic fonts loaded successfully');
        debugPrint('📄 Regular font: $_arabicFont');
        debugPrint('📄 Bold font: $_arabicBoldFont');
      } catch (e) {
        debugPrint('❌ Error loading Arabic fonts: $e');
        debugPrint('❌ Stack trace: ${StackTrace.current}');
        // Fallback to default fonts
        _arabicFont = pw.Font.helvetica();
        _arabicBoldFont = pw.Font.helveticaBold();
        debugPrint('⚠️ Using fallback fonts');
      }
    } else {
      debugPrint('📄 Arabic fonts already loaded');
    }
  }

  static Future<String?> generateReport(
    ReportData reportData, {
    PersonalInfo? personalInfo,
  }) async {
    try {
      debugPrint('📄 Starting PDF generation for: ${reportData.title}');

      // Load Arabic fonts first
      await _loadArabicFonts();

      final pdf = pw.Document();

      debugPrint('📄 Adding page with RTL support...');
      // Add page with RTL support
      // Build header with logo loading
      final header = await _buildHeader(reportData, personalInfo);

      pdf.addPage(
        pw.MultiPage(
          pageFormat: PdfPageFormat.a4,
          textDirection: pw.TextDirection.rtl,
          build: (pw.Context context) {
            return [
              header,
              pw.SizedBox(height: 20),
              _buildReportInfo(reportData),
              pw.SizedBox(height: 20),
              _buildContent(reportData),
            ];
          },
        ),
      );

      debugPrint('📄 Saving PDF file...');
      // Save file
      final directory = await getApplicationDocumentsDirectory();
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final fileName = 'report_$timestamp.pdf';
      final filePath = '${directory.path}/$fileName';

      debugPrint('📄 File path: $filePath');

      final file = File(filePath);
      final pdfBytes = await pdf.save();
      await file.writeAsBytes(pdfBytes);

      debugPrint('✅ PDF file saved successfully');
      return filePath;
    } catch (e, stackTrace) {
      debugPrint('❌ Error generating PDF report: $e');
      debugPrint('Stack trace: $stackTrace');
      return null;
    }
  }

  static Future<pw.Widget> _buildHeader(
    ReportData reportData,
    PersonalInfo? personalInfo,
  ) async {
    // Load logo if available
    pw.ImageProvider? logoImage;
    if (personalInfo?.logoPath != null) {
      logoImage = await _loadLogo(personalInfo!.logoPath);
    }
    return pw.Column(
      children: [
        // Header with logo and personal info
        pw.Row(
          mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
          crossAxisAlignment: pw.CrossAxisAlignment.start,
          children: [
            // Logo section (left side)
            pw.Container(
              width: 100,
              height: 100,
              decoration: pw.BoxDecoration(
                border: pw.Border.all(color: PdfColors.grey400),
                borderRadius: pw.BorderRadius.circular(8),
              ),
              child:
                  logoImage != null
                      ? pw.Image(logoImage, fit: pw.BoxFit.cover)
                      : pw.Center(
                        child: pw.Text(
                          'شعار',
                          style: pw.TextStyle(
                            fontSize: 12,
                            font: _arabicFont,
                            fontFallback: [pw.Font.helvetica()],
                            color: PdfColors.grey600,
                          ),
                          textDirection: pw.TextDirection.rtl,
                        ),
                      ),
            ),

            // Personal info section (right side)
            pw.Expanded(
              child: pw.Container(
                padding: const pw.EdgeInsets.only(right: 20),
                child: pw.Column(
                  crossAxisAlignment: pw.CrossAxisAlignment.end,
                  children: [
                    if (personalInfo?.fullName.isNotEmpty == true)
                      pw.Text(
                        personalInfo!.fullName,
                        style: pw.TextStyle(
                          fontSize: 18,
                          fontWeight: pw.FontWeight.bold,
                          font: _arabicBoldFont,
                          fontFallback: [pw.Font.helveticaBold()],
                          color: PdfColors.black,
                        ),
                        textDirection: pw.TextDirection.rtl,
                        textAlign: pw.TextAlign.right,
                      ),

                    if (personalInfo?.address.isNotEmpty == true) ...[
                      pw.SizedBox(height: 4),
                      pw.Text(
                        personalInfo!.address,
                        style: pw.TextStyle(
                          fontSize: 14,
                          font: _arabicFont,
                          fontFallback: [pw.Font.helvetica()],
                          color: PdfColors.grey700,
                        ),
                        textDirection: pw.TextDirection.rtl,
                        textAlign: pw.TextAlign.right,
                      ),
                    ],

                    if (personalInfo?.mobileNumber.isNotEmpty == true) ...[
                      pw.SizedBox(height: 4),
                      pw.Text(
                        personalInfo!.mobileNumber,
                        style: pw.TextStyle(
                          fontSize: 14,
                          font: _arabicFont,
                          fontFallback: [pw.Font.helvetica()],
                          color: PdfColors.grey700,
                        ),
                        textDirection:
                            pw.TextDirection.ltr, // LTR for phone numbers
                        textAlign: pw.TextAlign.right,
                      ),
                    ],

                    if (personalInfo?.emailAddress.isNotEmpty == true) ...[
                      pw.SizedBox(height: 4),
                      pw.Text(
                        personalInfo!.emailAddress,
                        style: pw.TextStyle(
                          fontSize: 14,
                          font: _arabicFont,
                          fontFallback: [pw.Font.helvetica()],
                          color: PdfColors.grey700,
                        ),
                        textDirection:
                            pw.TextDirection.ltr, // LTR for email addresses
                        textAlign: pw.TextAlign.right,
                      ),
                    ],
                  ],
                ),
              ),
            ),
          ],
        ),

        pw.SizedBox(height: 30),
      ],
    );
  }

  static pw.Widget _buildReportInfo(ReportData reportData) {
    return pw
        .SizedBox.shrink(); // Remove report info section to match the image
  }

  static pw.Widget _buildInfoRow(String label, String value) {
    return pw.Padding(
      padding: const pw.EdgeInsets.only(bottom: 4),
      child: pw.Row(
        children: [
          pw.Text(
            label,
            style: pw.TextStyle(
              fontSize: 11,
              fontWeight: pw.FontWeight.bold,
              font: _arabicBoldFont,
              fontFallback: [pw.Font.helveticaBold()],
              color: PdfColors.grey700,
            ),
            textDirection: pw.TextDirection.rtl,
          ),
          pw.SizedBox(width: 8),
          pw.Text(
            value,
            style: pw.TextStyle(
              fontSize: 11,
              font: _arabicFont,
              fontFallback: [pw.Font.helvetica()],
              color: PdfColors.grey800,
            ),
            textDirection: pw.TextDirection.rtl,
          ),
        ],
      ),
    );
  }

  static pw.Widget _buildContent(ReportData reportData) {
    switch (reportData.config.type) {
      case ReportType.accountBalanceSummary:
        return _buildAccountBalanceSummary(reportData);
      case ReportType.detailedAccountBalance:
        return _buildDetailedAccountBalance(reportData);
      case ReportType.monthlyAccountBalance:
        return _buildMonthlyAccountBalance(reportData);
      case ReportType.categoryAndCurrencySummary:
        return _buildCategoryAndCurrencySummary(reportData);
      case ReportType.monthlyCategoryDetail:
        return _buildMonthlyCategoryDetail(reportData);
    }
  }

  static pw.Widget _buildAccountBalanceSummary(ReportData reportData) {
    final data = reportData.data;
    final accounts = data['accounts'] as List<Map<String, dynamic>>? ?? [];
    final summary = data['summary'] as Map<String, dynamic>? ?? {};
    final currency = reportData.config.currency;

    // Calculate total for "عليه" accounts only (matching the image)
    double totalOwedAmount = 0;
    for (final account in accounts) {
      final debtType = account['debtType'];
      final balance = account['balance'] ?? 0;

      // Only sum "عليه" accounts (iOwe = 1)
      if (debtType == 1 || debtType == DebtType.iOwe) {
        totalOwedAmount += balance.abs();
      }
    }

    return pw.Column(
      children: [
        // Main Table matching the image design
        pw.Directionality(
          textDirection: pw.TextDirection.rtl,
          child: pw.Container(
            decoration: pw.BoxDecoration(
              border: pw.Border.all(color: PdfColors.black, width: 1),
            ),
            child: pw.Table(
              border: pw.TableBorder.all(color: PdfColors.black),
              columnWidths: {
                0: const pw.FlexColumnWidth(
                  3,
                ), // اسم الحساب (أول عمود من اليمين)
                1: const pw.FlexColumnWidth(2), // الرصيد
                2: const pw.FlexColumnWidth(2), // نوع الحساب
                3: const pw.FlexColumnWidth(2), // التاريخ (آخر عمود من اليسار)
              },
              children: [
                // Header Row
                pw.TableRow(
                  decoration: const pw.BoxDecoration(color: PdfColors.grey300),
                  children: [
                    _buildSimpleTableCell('اسم الحساب', isHeader: true),
                    _buildSimpleTableCell('الرصيد', isHeader: true),
                    _buildSimpleTableCell('نوع الحساب', isHeader: true),
                    _buildSimpleTableCell('التاريخ', isHeader: true),
                  ],
                ),

                // Data Rows
                ...accounts.map((account) {
                  return pw.TableRow(
                    children: [
                      _buildSimpleTableCell(account['name'] ?? ''),
                      _buildSimpleTableCell('${account['balance'] ?? 0}'),
                      _buildSimpleTableCell(
                        _getDebtTypeText(account['debtType'] ?? 0),
                      ),
                      _buildSimpleTableCell(_formatDate(DateTime.now())),
                    ],
                  );
                }),

                // Total Row (matching the red row in image)
                pw.TableRow(
                  decoration: const pw.BoxDecoration(color: PdfColors.red300),
                  children: [
                    _buildSimpleTableCell(
                      'الرصيد الإجمالي - عليه',
                      isHeader: true,
                      textColor: PdfColors.blue800,
                    ),
                    _buildSimpleTableCell(
                      '$totalOwedAmount',
                      isHeader: true,
                      textColor: PdfColors.blue800,
                    ),
                    _buildSimpleTableCell('', isHeader: true),
                    _buildSimpleTableCell('', isHeader: true),
                  ],
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  static pw.Widget _buildDetailedAccountBalance(ReportData reportData) {
    final data = reportData.data;
    final accountDetails =
        data['accountDetails'] as List<Map<String, dynamic>>? ?? [];

    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        pw.Text(
          'تفاصيل الحسابات',
          style: pw.TextStyle(
            fontSize: 18,
            fontWeight: pw.FontWeight.bold,
            font: _arabicBoldFont,
            fontFallback: [pw.Font.helveticaBold()],
          ),
          textDirection: pw.TextDirection.rtl,
        ),
        pw.SizedBox(height: 10),

        if (accountDetails.isNotEmpty)
          pw.Table(
            border: pw.TableBorder.all(color: PdfColors.grey),
            children: [
              // Header
              pw.TableRow(
                decoration: const pw.BoxDecoration(color: PdfColors.grey200),
                children: [
                  _buildTableCell('الاسم', isHeader: true),
                  _buildTableCell('رقم الهاتف', isHeader: true),
                  _buildTableCell('العنوان', isHeader: true),
                  _buildTableCell('الرصيد', isHeader: true),
                  _buildTableCell('النوع', isHeader: true),
                ],
              ),
              // Data rows
              ...accountDetails.map((detail) {
                final account = detail['account'] as Map<String, dynamic>;
                final balance = detail['balance'] ?? 0;

                return pw.TableRow(
                  children: [
                    _buildTableCell(account['name'] ?? ''),
                    _buildTableCell(account['phoneNumber'] ?? ''),
                    _buildTableCell(account['address'] ?? ''),
                    _buildTableCell(_formatNumber(balance)),
                    _buildTableCell(_getDebtTypeText(account['debtType'] ?? 0)),
                  ],
                );
              }),
            ],
          ),
      ],
    );
  }

  static pw.Widget _buildMonthlyAccountBalance(ReportData reportData) {
    final data = reportData.data;
    final monthlyData = data['monthlyData'] as Map<String, dynamic>? ?? {};

    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        pw.Text(
          'الأرصدة الشهرية',
          style: pw.TextStyle(
            fontSize: 18,
            fontWeight: pw.FontWeight.bold,
            font: _arabicBoldFont,
            fontFallback: [pw.Font.helveticaBold()],
          ),
          textDirection: pw.TextDirection.rtl,
        ),
        pw.SizedBox(height: 10),

        if (monthlyData.isNotEmpty)
          pw.Table(
            border: pw.TableBorder.all(color: PdfColors.grey),
            children: [
              // Header
              pw.TableRow(
                decoration: const pw.BoxDecoration(color: PdfColors.grey200),
                children: [
                  _buildTableCell('الشهر', isHeader: true),
                  _buildTableCell('إجمالي لي', isHeader: true),
                  _buildTableCell('إجمالي علي', isHeader: true),
                  _buildTableCell('الصافي', isHeader: true),
                  _buildTableCell('عدد الحسابات', isHeader: true),
                ],
              ),
              // Data rows
              ...monthlyData.entries.map((entry) {
                final monthData = entry.value as Map<String, dynamic>;
                return pw.TableRow(
                  children: [
                    _buildTableCell(entry.key),
                    _buildTableCell(
                      _formatNumber(monthData['totalOwedToMe'] ?? 0),
                    ),
                    _buildTableCell(_formatNumber(monthData['totalIOwe'] ?? 0)),
                    _buildTableCell(
                      _formatNumber(monthData['netBalance'] ?? 0),
                    ),
                    _buildTableCell('${monthData['accountCount'] ?? 0}'),
                  ],
                );
              }),
            ],
          ),
      ],
    );
  }

  static pw.Widget _buildCategoryAndCurrencySummary(ReportData reportData) {
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        pw.Text(
          'ملخص الفئات والعملات',
          style: pw.TextStyle(
            fontSize: 18,
            fontWeight: pw.FontWeight.bold,
            font: _arabicBoldFont,
            fontFallback: [pw.Font.helveticaBold()],
          ),
          textDirection: pw.TextDirection.rtl,
        ),
        pw.SizedBox(height: 10),
        pw.Text(
          'سيتم إضافة تفاصيل الفئات والعملات هنا',
          style: pw.TextStyle(
            fontSize: 12,
            font: _arabicFont,
            fontFallback: [pw.Font.helvetica()],
          ),
          textDirection: pw.TextDirection.rtl,
        ),
      ],
    );
  }

  static pw.Widget _buildMonthlyCategoryDetail(ReportData reportData) {
    return _buildMonthlyAccountBalance(reportData);
  }

  static pw.Widget _buildSummaryRow(String label, String value) {
    return pw.Padding(
      padding: const pw.EdgeInsets.symmetric(vertical: 2),
      child: pw.Row(
        mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
        children: [
          pw.Text(
            label,
            textDirection: pw.TextDirection.rtl,
            style: pw.TextStyle(
              font: _arabicFont,
              fontFallback: [pw.Font.helvetica()],
            ),
          ),
          pw.Text(
            value,
            textDirection: pw.TextDirection.rtl,
            style: pw.TextStyle(
              font: _arabicFont,
              fontFallback: [pw.Font.helvetica()],
            ),
          ),
        ],
      ),
    );
  }

  static pw.Widget _buildTableCell(String text, {bool isHeader = false}) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(5),
      child: pw.Text(
        text,
        style: pw.TextStyle(
          fontSize: isHeader ? 12 : 10,
          fontWeight: isHeader ? pw.FontWeight.bold : pw.FontWeight.normal,
          font: isHeader ? _arabicBoldFont : _arabicFont,
          fontFallback: [pw.Font.helvetica()],
        ),
        textDirection: pw.TextDirection.rtl,
        textAlign: pw.TextAlign.center,
      ),
    );
  }

  static pw.Widget _buildSimpleTableCell(
    String text, {
    bool isHeader = false,
    PdfColor? textColor,
  }) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(8),
      child: pw.Text(
        text,
        style: pw.TextStyle(
          fontSize: isHeader ? 12 : 11,
          fontWeight: isHeader ? pw.FontWeight.bold : pw.FontWeight.normal,
          font: isHeader ? _arabicBoldFont : _arabicFont,
          fontFallback: [pw.Font.helvetica()],
          color: textColor ?? PdfColors.black,
        ),
        textDirection: pw.TextDirection.rtl,
        textAlign: pw.TextAlign.center,
      ),
    );
  }

  static pw.Widget _buildEnhancedTableCell(
    String text, {
    bool isHeader = false,
    bool isAmount = false,
    double? amount,
    bool isDebtType = false,
    dynamic debtType,
  }) {
    PdfColor? textColor;

    if (isHeader) {
      textColor = PdfColors.white;
    } else if (isAmount && amount != null) {
      textColor = amount >= 0 ? PdfColors.green700 : PdfColors.red700;
    } else if (isDebtType) {
      if (debtType == 0 || debtType == DebtType.owedToMe) {
        textColor = PdfColors.green700;
      } else {
        textColor = PdfColors.red700;
      }
    }

    return pw.Container(
      padding: const pw.EdgeInsets.symmetric(horizontal: 8, vertical: 12),
      child: pw.Text(
        text,
        style: pw.TextStyle(
          fontSize: isHeader ? 11 : 10,
          fontWeight: isHeader ? pw.FontWeight.bold : pw.FontWeight.normal,
          font: isHeader ? _arabicBoldFont : _arabicFont,
          fontFallback: [pw.Font.helvetica()],
          color: textColor ?? (isHeader ? PdfColors.white : PdfColors.grey800),
        ),
        textDirection: pw.TextDirection.rtl,
        textAlign: pw.TextAlign.center,
      ),
    );
  }

  static String _formatDateTime(DateTime dateTime) {
    return '${dateTime.day}/${dateTime.month}/${dateTime.year} ${dateTime.hour}:${dateTime.minute.toString().padLeft(2, '0')}';
  }

  static String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  static String _formatNumber(double number) {
    return number.toStringAsFixed(2);
  }

  static String _formatNumberWithCommas(double number) {
    final formatter = RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))');
    String result = number.toStringAsFixed(2);
    return result.replaceAllMapped(formatter, (Match m) => '${m[1]},');
  }

  static pw.Widget _buildSummaryCard(
    String title,
    String value,
    PdfColor titleColor,
    PdfColor backgroundColor,
  ) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(15),
      decoration: pw.BoxDecoration(
        color: backgroundColor,
        border: pw.Border.all(color: titleColor, width: 1.5),
        borderRadius: pw.BorderRadius.circular(8),
      ),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.center,
        children: [
          pw.Text(
            title,
            style: pw.TextStyle(
              fontSize: 11,
              fontWeight: pw.FontWeight.bold,
              font: _arabicBoldFont,
              fontFallback: [pw.Font.helveticaBold()],
              color: titleColor,
            ),
            textDirection: pw.TextDirection.rtl,
            textAlign: pw.TextAlign.center,
          ),
          pw.SizedBox(height: 8),
          pw.Text(
            value,
            style: pw.TextStyle(
              fontSize: 14,
              fontWeight: pw.FontWeight.bold,
              font: _arabicBoldFont,
              fontFallback: [pw.Font.helveticaBold()],
              color: titleColor,
            ),
            textDirection: pw.TextDirection.rtl,
            textAlign: pw.TextAlign.center,
          ),
        ],
      ),
    );
  }

  static String _getDebtTypeText(dynamic debtType) {
    if (debtType is DebtType) {
      switch (debtType) {
        case DebtType.owedToMe:
          return 'له';
        case DebtType.iOwe:
          return 'عليه';
      }
    } else if (debtType is int) {
      switch (debtType) {
        case 0:
          return 'له';
        case 1:
          return 'عليه';
        default:
          return 'غير محدد';
      }
    }
    return 'غير محدد';
  }
}
