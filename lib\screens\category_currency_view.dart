import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/category.dart' as model;
import '../models/currency.dart';
import '../models/account.dart';
import '../providers/account_provider.dart';

import '../widgets/account_card.dart';
import '../utils/constants.dart';
import '../widgets/simple_add_account_modal.dart';
import 'account_list_screen.dart';
import 'account_details_screen.dart';

class CategoryCurrencyView extends StatefulWidget {
  final model.Category category;
  final VoidCallback? onRefresh;

  const CategoryCurrencyView({
    super.key,
    required this.category,
    this.onRefresh,
  });

  @override
  State<CategoryCurrencyView> createState() => _CategoryCurrencyViewState();
}

class _CategoryCurrencyViewState extends State<CategoryCurrencyView> {
  List<Currency> _currenciesInCategory = [];
  bool _isLoading = false;
  PageController? _pageController;
  int _currentPageIndex = 0;

  @override
  void initState() {
    super.initState();
    _loadCurrenciesInCategory();
  }

  @override
  void dispose() {
    _pageController?.dispose();
    super.dispose();
  }

  Future<void> _loadCurrenciesInCategory() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final accountProvider = context.read<AccountProvider>();
      final currencies = await accountProvider.getCurrenciesUsedInCategory(
        widget.category.id!,
      );

      setState(() {
        _currenciesInCategory = currencies;
        // Initialize PageController when currencies are loaded
        if (currencies.isNotEmpty && _pageController == null) {
          _pageController = PageController();
        }
      });
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحميل العملات: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _navigateToAccountList(Currency currency) async {
    final result = await Navigator.of(context).push(
      MaterialPageRoute(
        builder:
            (context) => AccountListScreen(
              category: widget.category,
              currency: currency,
            ),
      ),
    );

    if (result == true) {
      widget.onRefresh?.call();
      _loadCurrenciesInCategory();
    }
  }

  Future<void> _navigateToAddAccount() async {
    final result = await SimpleAddAccountModal.show(context);

    if (result == true) {
      widget.onRefresh?.call();
      _loadCurrenciesInCategory();
    }
  }

  Future<void> _navigateToAccountDetails(
    Account account,
    Currency currency,
  ) async {
    final result = await Navigator.of(context).push(
      MaterialPageRoute(
        builder:
            (context) => AccountDetailsScreen(
              account: account,
              currency: currency,
              category: widget.category,
            ),
      ),
    );

    if (result == true) {
      widget.onRefresh?.call();
      _loadCurrenciesInCategory();
    }
  }

  Future<void> _deleteAccount(Account account) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('تأكيد الحذف'),
            content: Text('هل أنت متأكد من حذف حساب "${account.name}"؟'),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(false),
                child: const Text(AppConstants.cancel),
              ),
              TextButton(
                onPressed: () => Navigator.of(context).pop(true),
                style: TextButton.styleFrom(
                  foregroundColor: Theme.of(context).colorScheme.error,
                ),
                child: const Text(AppConstants.delete),
              ),
            ],
          ),
    );

    if (confirmed == true && mounted) {
      final accountProvider = context.read<AccountProvider>();
      final success = await accountProvider.deleteAccount(account.id!);

      if (success) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text(AppConstants.deleteSuccess),
              backgroundColor: Colors.green,
            ),
          );
          widget.onRefresh?.call();
          _loadCurrenciesInCategory();
        }
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(accountProvider.error ?? 'فشل في حذف الحساب'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }

  Future<void> _editAccount(Account account) async {
    final result = await SimpleAddAccountModal.show(
      context,
      accountToEdit: account,
    );

    if (result == true) {
      widget.onRefresh?.call();
      _loadCurrenciesInCategory();
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_currenciesInCategory.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.account_balance_wallet_outlined,
              size: 64,
              color: Theme.of(
                context,
              ).colorScheme.onSurface.withValues(alpha: 0.5),
            ),
            const SizedBox(height: 16),
            Text(
              'لا توجد حسابات في فئة "${widget.category.name}"',
              style: Theme.of(context).textTheme.titleLarge,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              'قم بإضافة حساب جديد للبدء',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(
                  context,
                ).colorScheme.onSurface.withValues(alpha: 0.7),
              ),
            ),
            const SizedBox(height: 16),
            ElevatedButton.icon(
              onPressed: _navigateToAddAccount,
              icon: const Icon(Icons.add),
              label: const Text(AppConstants.addAccount),
            ),
          ],
        ),
      );
    }

    // Single currency - show directly without PageView
    if (_currenciesInCategory.length == 1) {
      return _buildCurrencyPage(_currenciesInCategory.first);
    }

    // Multiple currencies - show PageView with indicators
    return Column(
      children: [
        // Currency indicators
        _buildCurrencyIndicators(),
        // PageView for currency pages
        Expanded(
          child: PageView.builder(
            controller: _pageController,
            onPageChanged: (index) {
              setState(() {
                _currentPageIndex = index;
              });
            },
            itemCount: _currenciesInCategory.length,
            itemBuilder: (context, index) {
              return _buildCurrencyPage(_currenciesInCategory[index]);
            },
          ),
        ),
      ],
    );
  }

  Widget _buildCurrencyIndicators() {
    if (_currenciesInCategory.length <= 1) {
      return const SizedBox.shrink();
    }

    return Container(
      padding: const EdgeInsets.symmetric(
        vertical: AppConstants.smallPadding,
        horizontal: AppConstants.defaultPadding,
      ),
      child: Column(
        children: [
          // Currency tabs
          SizedBox(
            height: 50,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: _currenciesInCategory.length,
              itemBuilder: (context, index) {
                final currency = _currenciesInCategory[index];
                final isSelected = index == _currentPageIndex;

                return GestureDetector(
                  onTap: () {
                    _pageController?.animateToPage(
                      index,
                      duration: const Duration(milliseconds: 300),
                      curve: Curves.easeInOut,
                    );
                  },
                  child: Container(
                    margin: const EdgeInsets.symmetric(horizontal: 4),
                    padding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 8,
                    ),
                    decoration: BoxDecoration(
                      color:
                          isSelected
                              ? Theme.of(context).colorScheme.primary
                              : Theme.of(context).colorScheme.surface,
                      borderRadius: BorderRadius.circular(20),
                      border: Border.all(
                        color:
                            isSelected
                                ? Theme.of(context).colorScheme.primary
                                : Theme.of(context).colorScheme.outline,
                      ),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          currency.symbol,
                          style: TextStyle(
                            color:
                                isSelected
                                    ? Theme.of(context).colorScheme.onPrimary
                                    : Theme.of(context).colorScheme.onSurface,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(width: 4),
                        Text(
                          currency.name,
                          style: TextStyle(
                            color:
                                isSelected
                                    ? Theme.of(context).colorScheme.onPrimary
                                    : Theme.of(context).colorScheme.onSurface,
                            fontSize: 12,
                          ),
                        ),
                      ],
                    ),
                  ),
                );
              },
            ),
          ),
          const SizedBox(height: 8),
          // Page dots
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children:
                _currenciesInCategory.asMap().entries.map((entry) {
                  final index = entry.key;
                  final isSelected = index == _currentPageIndex;

                  return Container(
                    margin: const EdgeInsets.symmetric(horizontal: 3),
                    width: isSelected ? 12 : 8,
                    height: 8,
                    decoration: BoxDecoration(
                      color:
                          isSelected
                              ? Theme.of(context).colorScheme.primary
                              : Theme.of(context).colorScheme.outline,
                      borderRadius: BorderRadius.circular(4),
                    ),
                  );
                }).toList(),
          ),
        ],
      ),
    );
  }

  Widget _buildCurrencyPage(Currency currency) {
    return Consumer<AccountProvider>(
      builder: (context, accountProvider, child) {
        // Check if IDs are valid
        if (widget.category.id == null || currency.id == null) {
          return const Center(child: Text('خطأ في تحميل البيانات'));
        }

        final totalOwedToMe = accountProvider.getTotalOwedToMe(
          widget.category.id!,
          currency.id!,
        );
        final totalIOwe = accountProvider.getTotalIOwe(
          widget.category.id!,
          currency.id!,
        );

        // Get all accounts for this currency and category
        final accounts = accountProvider.getAccountsByCategoryAndCurrency(
          widget.category.id!,
          currency.id!,
        );

        return RefreshIndicator(
          onRefresh: () async {
            widget.onRefresh?.call();
            await _loadCurrenciesInCategory();
          },
          child: CustomScrollView(
            slivers: [
              // Summary Card
              SliverToBoxAdapter(
                child: AccountSummaryCard(
                  title: '${currency.name} (${currency.symbol})',
                  totalOwedToMe: totalOwedToMe,
                  totalIOwe: totalIOwe,
                  currency: currency,
                  onTap: () => _navigateToAccountList(currency),
                ),
              ),

              // Accounts List
              if (accounts.isEmpty)
                SliverFillRemaining(
                  child: Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.account_balance_wallet_outlined,
                          size: 48,
                          color: Theme.of(
                            context,
                          ).colorScheme.onSurface.withValues(alpha: 0.5),
                        ),
                        const SizedBox(height: 16),
                        Text(
                          'لا توجد حسابات بعملة ${currency.name}',
                          style: Theme.of(context).textTheme.titleMedium,
                          textAlign: TextAlign.center,
                        ),
                        const SizedBox(height: 8),
                        Text(
                          'قم بإضافة حساب جديد',
                          style: Theme.of(
                            context,
                          ).textTheme.bodyMedium?.copyWith(
                            color: Theme.of(
                              context,
                            ).colorScheme.onSurface.withValues(alpha: 0.7),
                          ),
                        ),
                        const SizedBox(height: 16),
                        ElevatedButton.icon(
                          onPressed: _navigateToAddAccount,
                          icon: const Icon(Icons.add),
                          label: const Text(AppConstants.addAccount),
                        ),
                      ],
                    ),
                  ),
                )
              else
                SliverPadding(
                  padding: const EdgeInsets.all(AppConstants.defaultPadding),
                  sliver: SliverList(
                    delegate: SliverChildBuilderDelegate((context, index) {
                      final account = accounts[index];
                      return Padding(
                        padding: const EdgeInsets.only(
                          bottom: AppConstants.smallPadding,
                        ),
                        child: AccountCard(
                          account: account,
                          currency: currency,
                          onEdit: () => _editAccount(account),
                          onDelete: () => _deleteAccount(account),
                          onTap:
                              () =>
                                  _navigateToAccountDetails(account, currency),
                        ),
                      );
                    }, childCount: accounts.length),
                  ),
                ),
            ],
          ),
        );
      },
    );
  }
}
