import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/category.dart' as model;
import '../models/currency.dart';
import '../models/account.dart';
import '../providers/account_provider.dart';

import '../widgets/account_card.dart';
import '../utils/constants.dart';
import '../widgets/add_account_modal.dart';
import 'account_list_screen.dart';

class CategoryCurrencyView extends StatefulWidget {
  final model.Category category;
  final VoidCallback? onRefresh;

  const CategoryCurrencyView({
    super.key,
    required this.category,
    this.onRefresh,
  });

  @override
  State<CategoryCurrencyView> createState() => _CategoryCurrencyViewState();
}

class _CategoryCurrencyViewState extends State<CategoryCurrencyView> {
  List<Currency> _currenciesInCategory = [];
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _loadCurrenciesInCategory();
  }

  Future<void> _loadCurrenciesInCategory() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final accountProvider = context.read<AccountProvider>();
      final currencies = await accountProvider.getCurrenciesUsedInCategory(
        widget.category.id!,
      );

      setState(() {
        _currenciesInCategory = currencies;
      });
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحميل العملات: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _navigateToAccountList(Currency currency) async {
    final result = await Navigator.of(context).push(
      MaterialPageRoute(
        builder:
            (context) => AccountListScreen(
              category: widget.category,
              currency: currency,
            ),
      ),
    );

    if (result == true) {
      widget.onRefresh?.call();
      _loadCurrenciesInCategory();
    }
  }

  Future<void> _navigateToAddAccount() async {
    final result = await AddAccountModal.show(context);

    if (result == true) {
      widget.onRefresh?.call();
      _loadCurrenciesInCategory();
    }
  }

  Future<void> _deleteAccount(Account account) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('تأكيد الحذف'),
            content: Text('هل أنت متأكد من حذف حساب "${account.name}"؟'),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(false),
                child: const Text(AppConstants.cancel),
              ),
              TextButton(
                onPressed: () => Navigator.of(context).pop(true),
                style: TextButton.styleFrom(
                  foregroundColor: Theme.of(context).colorScheme.error,
                ),
                child: const Text(AppConstants.delete),
              ),
            ],
          ),
    );

    if (confirmed == true) {
      final accountProvider = context.read<AccountProvider>();
      final success = await accountProvider.deleteAccount(account.id!);

      if (success) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text(AppConstants.deleteSuccess),
              backgroundColor: Colors.green,
            ),
          );
          widget.onRefresh?.call();
          _loadCurrenciesInCategory();
        }
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(accountProvider.error ?? 'فشل في حذف الحساب'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }

  Future<void> _editAccount(Account account) async {
    final result = await AddAccountModal.show(context, accountToEdit: account);

    if (result == true) {
      widget.onRefresh?.call();
      _loadCurrenciesInCategory();
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_currenciesInCategory.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.account_balance_wallet_outlined,
              size: 64,
              color: Theme.of(
                context,
              ).colorScheme.onSurface.withValues(alpha: 0.5),
            ),
            const SizedBox(height: 16),
            Text(
              'لا توجد حسابات في فئة "${widget.category.name}"',
              style: Theme.of(context).textTheme.titleLarge,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              'قم بإضافة حساب جديد للبدء',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(
                  context,
                ).colorScheme.onSurface.withValues(alpha: 0.7),
              ),
            ),
            const SizedBox(height: 16),
            ElevatedButton.icon(
              onPressed: _navigateToAddAccount,
              icon: const Icon(Icons.add),
              label: const Text(AppConstants.addAccount),
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: () async {
        widget.onRefresh?.call();
        await _loadCurrenciesInCategory();
      },
      child: ListView.builder(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        itemCount: _currenciesInCategory.length,
        itemBuilder: (context, index) {
          final currency = _currenciesInCategory[index];

          return Consumer<AccountProvider>(
            builder: (context, accountProvider, child) {
              final totalOwedToMe = accountProvider.getTotalOwedToMe(
                widget.category.id!,
                currency.id!,
              );
              final totalIOwe = accountProvider.getTotalIOwe(
                widget.category.id!,
                currency.id!,
              );

              // Get recent accounts for preview
              final accounts =
                  accountProvider
                      .getAccountsByCategoryAndCurrency(
                        widget.category.id!,
                        currency.id!,
                      )
                      .take(3)
                      .toList();

              return Card(
                margin: const EdgeInsets.only(
                  bottom: AppConstants.defaultPadding,
                ),
                elevation: 4,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(
                    AppConstants.defaultBorderRadius,
                  ),
                ),
                child: Column(
                  children: [
                    // Currency Summary Header
                    AccountSummaryCard(
                      title: '${currency.name} (${currency.symbol})',
                      totalOwedToMe: totalOwedToMe,
                      totalIOwe: totalIOwe,
                      currency: currency,
                      onTap: () => _navigateToAccountList(currency),
                    ),

                    // Recent Accounts Preview
                    if (accounts.isNotEmpty) ...[
                      Padding(
                        padding: const EdgeInsets.symmetric(
                          horizontal: AppConstants.defaultPadding,
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              'الحسابات الحديثة',
                              style: Theme.of(context).textTheme.titleMedium
                                  ?.copyWith(fontWeight: FontWeight.bold),
                            ),
                            TextButton(
                              onPressed: () => _navigateToAccountList(currency),
                              child: const Text('عرض الكل'),
                            ),
                          ],
                        ),
                      ),
                      ...accounts.map((account) {
                        return AccountCard(
                          account: account,
                          currency: currency,
                          onEdit: () => _editAccount(account),
                          onDelete: () => _deleteAccount(account),
                          onTap: () => _navigateToAccountList(currency),
                        );
                      }),
                      const SizedBox(height: AppConstants.defaultPadding),
                    ],
                  ],
                ),
              );
            },
          );
        },
      ),
    );
  }
}
