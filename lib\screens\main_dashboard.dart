import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/account_provider.dart';
import '../providers/category_provider.dart';
import '../providers/currency_provider.dart';
import '../utils/constants.dart';
import '../widgets/add_account_modal.dart';
import 'settings_screen.dart';
import 'category_currency_view.dart';

class MainDashboard extends StatefulWidget {
  const MainDashboard({super.key});

  @override
  State<MainDashboard> createState() => _MainDashboardState();
}

class _MainDashboardState extends State<MainDashboard>
    with TickerProviderStateMixin {
  TabController? _tabController;

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  Future<void> _loadData() async {
    final categoryProvider = context.read<CategoryProvider>();
    final currencyProvider = context.read<CurrencyProvider>();
    final accountProvider = context.read<AccountProvider>();

    await Future.wait([
      categoryProvider.loadCategories(),
      currencyProvider.loadCurrencies(),
    ]);

    await accountProvider.loadAccounts();

    _initializeTabController();
  }

  void _initializeTabController() {
    final categories = context.read<CategoryProvider>().categories;
    if (categories.isNotEmpty) {
      _tabController = TabController(length: categories.length, vsync: this);
    }
  }

  @override
  void dispose() {
    _tabController?.dispose();
    super.dispose();
  }

  Future<void> _navigateToAddAccount() async {
    final result = await AddAccountModal.show(context);

    if (result == true) {
      _refreshData();
    }
  }

  Future<void> _navigateToSettings() async {
    final result = await Navigator.of(
      context,
    ).push(MaterialPageRoute(builder: (context) => const SettingsScreen()));

    if (result == true) {
      _refreshData();
      _initializeTabController();
    }
  }

  Future<void> _refreshData() async {
    final accountProvider = context.read<AccountProvider>();
    await accountProvider.loadAccounts();
  }

  PreferredSizeWidget? _buildTabBar() {
    final categoryProvider = context.watch<CategoryProvider>();

    if (categoryProvider.isLoading) {
      return const PreferredSize(
        preferredSize: Size.fromHeight(4),
        child: LinearProgressIndicator(),
      );
    }

    if (categoryProvider.categories.isEmpty) {
      return const PreferredSize(
        preferredSize: Size.fromHeight(0),
        child: SizedBox.shrink(),
      );
    }

    if (_tabController == null ||
        _tabController!.length != categoryProvider.categories.length) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _initializeTabController();
      });
      return const PreferredSize(
        preferredSize: Size.fromHeight(0),
        child: SizedBox.shrink(),
      );
    }

    return TabBar(
      controller: _tabController,
      isScrollable: true,
      tabAlignment: TabAlignment.start,
      tabs:
          categoryProvider.categories.map((category) {
            return Tab(
              text: category.name,
              icon:
                  category.isDefault
                      ? const Icon(Icons.home, size: 16)
                      : const Icon(Icons.folder, size: 16),
            );
          }).toList(),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          AppConstants.appName,
          style: TextStyle(fontWeight: FontWeight.bold),
        ),
        backgroundColor: Theme.of(context).colorScheme.surface,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: _navigateToSettings,
            tooltip: AppConstants.settings,
          ),
        ],
        bottom: _buildTabBar(),
      ),
      body: Consumer3<CategoryProvider, CurrencyProvider, AccountProvider>(
        builder: (
          context,
          categoryProvider,
          currencyProvider,
          accountProvider,
          child,
        ) {
          if (categoryProvider.isLoading || currencyProvider.isLoading) {
            return const Center(child: CircularProgressIndicator());
          }

          if (categoryProvider.error != null) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.error_outline,
                    size: 64,
                    color: Theme.of(context).colorScheme.error,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    categoryProvider.error!,
                    style: Theme.of(context).textTheme.titleMedium,
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: _loadData,
                    child: const Text('إعادة المحاولة'),
                  ),
                ],
              ),
            );
          }

          if (categoryProvider.categories.isEmpty) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.folder_open,
                    size: 64,
                    color: Theme.of(
                      context,
                    ).colorScheme.onSurface.withValues(alpha: 0.5),
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'لا توجد فئات',
                    style: Theme.of(context).textTheme.titleLarge,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'قم بإنشاء فئة جديدة من الإعدادات',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Theme.of(
                        context,
                      ).colorScheme.onSurface.withValues(alpha: 0.7),
                    ),
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton.icon(
                    onPressed: _navigateToSettings,
                    icon: const Icon(Icons.settings),
                    label: const Text(AppConstants.settings),
                  ),
                ],
              ),
            );
          }

          if (_tabController == null) {
            return const Center(child: CircularProgressIndicator());
          }

          return TabBarView(
            controller: _tabController,
            children:
                categoryProvider.categories.map((category) {
                  return CategoryCurrencyView(
                    category: category,
                    onRefresh: _refreshData,
                  );
                }).toList(),
          );
        },
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: _navigateToAddAccount,
        icon: const Icon(Icons.add),
        label: const Text(AppConstants.addAccount),
        tooltip: AppConstants.addAccount,
      ),
    );
  }
}
