import 'constants.dart';

class Validators {
  // Name validation
  static String? validateName(String? value) {
    if (value == null || value.trim().isEmpty) {
      return AppConstants.requiredField;
    }
    
    if (value.trim().length > AppConstants.maxNameLength) {
      return AppConstants.nameTooLong;
    }
    
    return null;
  }

  // Phone number validation
  static String? validatePhoneNumber(String? value) {
    if (value == null || value.trim().isEmpty) {
      return AppConstants.requiredField;
    }
    
    // Remove spaces and formatting
    String cleanPhone = value.replaceAll(RegExp(r'\s+'), '');
    
    // Check general phone pattern
    if (!RegExp(AppConstants.phonePattern).hasMatch(cleanPhone)) {
      return AppConstants.invalidPhone;
    }
    
    return null;
  }

  // Saudi phone number validation
  static String? validateSaudiPhoneNumber(String? value) {
    if (value == null || value.trim().isEmpty) {
      return AppConstants.requiredField;
    }
    
    // Remove spaces and formatting
    String cleanPhone = value.replaceAll(RegExp(r'\s+'), '');
    
    // Check Saudi phone pattern
    if (!RegExp(AppConstants.saudiPhonePattern).hasMatch(cleanPhone)) {
      return 'رقم الهاتف السعودي غير صحيح (يجب أن يبدأ بـ 05)';
    }
    
    return null;
  }

  // Amount validation
  static String? validateAmount(String? value) {
    if (value == null || value.trim().isEmpty) {
      return AppConstants.requiredField;
    }
    
    double? amount = double.tryParse(value.trim());
    if (amount == null) {
      return AppConstants.invalidAmount;
    }
    
    if (amount <= 0) {
      return 'المبلغ يجب أن يكون أكبر من صفر';
    }
    
    if (amount > AppConstants.maxAmount) {
      return AppConstants.amountTooLarge;
    }
    
    return null;
  }

  // Details validation
  static String? validateDetails(String? value) {
    if (value != null && value.length > AppConstants.maxDetailsLength) {
      return AppConstants.detailsTooLong;
    }
    
    return null;
  }

  // Category name validation
  static String? validateCategoryName(String? value, List<String> existingNames) {
    if (value == null || value.trim().isEmpty) {
      return AppConstants.requiredField;
    }
    
    if (value.trim().length > AppConstants.maxNameLength) {
      return AppConstants.nameTooLong;
    }
    
    if (existingNames.contains(value.trim())) {
      return 'اسم الفئة موجود مسبقاً';
    }
    
    return null;
  }

  // Currency validation
  static String? validateCurrencyName(String? value, List<String> existingNames) {
    if (value == null || value.trim().isEmpty) {
      return AppConstants.requiredField;
    }
    
    if (value.trim().length > AppConstants.maxNameLength) {
      return AppConstants.nameTooLong;
    }
    
    if (existingNames.contains(value.trim())) {
      return 'اسم العملة موجود مسبقاً';
    }
    
    return null;
  }

  static String? validateCurrencySymbol(String? value) {
    if (value == null || value.trim().isEmpty) {
      return AppConstants.requiredField;
    }
    
    if (value.trim().length > 5) {
      return 'رمز العملة طويل جداً';
    }
    
    return null;
  }

  static String? validateCurrencyCode(String? value, List<String> existingCodes) {
    if (value == null || value.trim().isEmpty) {
      return AppConstants.requiredField;
    }
    
    String code = value.trim().toUpperCase();
    
    if (code.length != 3) {
      return 'كود العملة يجب أن يكون 3 أحرف';
    }
    
    if (!RegExp(r'^[A-Z]{3}$').hasMatch(code)) {
      return 'كود العملة يجب أن يحتوي على أحرف إنجليزية فقط';
    }
    
    if (existingCodes.contains(code)) {
      return 'كود العملة موجود مسبقاً';
    }
    
    return null;
  }

  // Email validation (optional for future use)
  static String? validateEmail(String? value) {
    if (value == null || value.trim().isEmpty) {
      return null; // Email is optional
    }
    
    const emailPattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$';
    if (!RegExp(emailPattern).hasMatch(value.trim())) {
      return 'البريد الإلكتروني غير صحيح';
    }
    
    return null;
  }

  // Date validation
  static String? validateDate(DateTime? value) {
    if (value == null) {
      return AppConstants.requiredField;
    }
    
    // Check if date is not in the future (more than 1 day)
    final now = DateTime.now();
    final tomorrow = DateTime(now.year, now.month, now.day + 1);
    
    if (value.isAfter(tomorrow)) {
      return 'التاريخ لا يمكن أن يكون في المستقبل';
    }
    
    // Check if date is not too old (more than 10 years)
    final tenYearsAgo = DateTime(now.year - 10, now.month, now.day);
    
    if (value.isBefore(tenYearsAgo)) {
      return 'التاريخ قديم جداً';
    }
    
    return null;
  }

  // Generic required field validation
  static String? validateRequired(dynamic value, [String? fieldName]) {
    if (value == null || 
        (value is String && value.trim().isEmpty) ||
        (value is List && value.isEmpty)) {
      return fieldName != null ? '$fieldName مطلوب' : AppConstants.requiredField;
    }
    
    return null;
  }

  // Dropdown validation
  static String? validateDropdown(dynamic value, [String? fieldName]) {
    if (value == null) {
      return fieldName != null ? 'يرجى اختيار $fieldName' : 'يرجى الاختيار من القائمة';
    }
    
    return null;
  }

  // Numeric validation
  static String? validateNumeric(String? value, [String? fieldName]) {
    if (value == null || value.trim().isEmpty) {
      return fieldName != null ? '$fieldName مطلوب' : AppConstants.requiredField;
    }
    
    if (double.tryParse(value.trim()) == null) {
      return fieldName != null ? '$fieldName يجب أن يكون رقماً' : 'يجب أن يكون رقماً';
    }
    
    return null;
  }

  // Positive number validation
  static String? validatePositiveNumber(String? value, [String? fieldName]) {
    String? numericValidation = validateNumeric(value, fieldName);
    if (numericValidation != null) return numericValidation;
    
    double number = double.parse(value!.trim());
    if (number <= 0) {
      return fieldName != null ? '$fieldName يجب أن يكون أكبر من صفر' : 'يجب أن يكون أكبر من صفر';
    }
    
    return null;
  }

  // Length validation
  static String? validateLength(String? value, int minLength, int maxLength, [String? fieldName]) {
    if (value == null || value.trim().isEmpty) {
      return fieldName != null ? '$fieldName مطلوب' : AppConstants.requiredField;
    }
    
    int length = value.trim().length;
    
    if (length < minLength) {
      return fieldName != null 
          ? '$fieldName يجب أن يكون على الأقل $minLength أحرف'
          : 'يجب أن يكون على الأقل $minLength أحرف';
    }
    
    if (length > maxLength) {
      return fieldName != null 
          ? '$fieldName يجب أن يكون أقل من $maxLength حرف'
          : 'يجب أن يكون أقل من $maxLength حرف';
    }
    
    return null;
  }
}
