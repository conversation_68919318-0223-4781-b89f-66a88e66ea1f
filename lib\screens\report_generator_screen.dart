import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/report.dart';
import '../models/currency.dart';
import '../models/category.dart' as model;
import '../providers/currency_provider.dart';
import '../providers/category_provider.dart';
import '../providers/personal_info_provider.dart';
import '../utils/constants.dart';
import '../services/report_service.dart';

class ReportGeneratorScreen extends StatefulWidget {
  final ReportType reportType;

  const ReportGeneratorScreen({
    super.key,
    required this.reportType,
  });

  @override
  State<ReportGeneratorScreen> createState() => _ReportGeneratorScreenState();
}

class _ReportGeneratorScreenState extends State<ReportGeneratorScreen> {
  Currency? _selectedCurrency;
  model.Category? _selectedCategory;
  DateTime? _startDate;
  DateTime? _endDate;
  ExportFormat _selectedFormat = ExportFormat.excel;
  bool _isGenerating = false;

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  Future<void> _loadData() async {
    final currencyProvider = Provider.of<CurrencyProvider>(context, listen: false);
    final categoryProvider = Provider.of<CategoryProvider>(context, listen: false);
    final personalInfoProvider = Provider.of<PersonalInfoProvider>(context, listen: false);
    
    await Future.wait([
      currencyProvider.loadCurrencies(),
      categoryProvider.loadCategories(),
      personalInfoProvider.loadPersonalInfo(),
    ]);

    // Set default currency
    if (currencyProvider.currencies.isNotEmpty) {
      setState(() {
        _selectedCurrency = currencyProvider.currencies.first;
      });
    }

    // Set default category if required
    if (widget.reportType.requiresCategory && categoryProvider.categories.isNotEmpty) {
      setState(() {
        _selectedCategory = categoryProvider.categories.first;
      });
    }

    // Set default date range if required
    if (widget.reportType.requiresDateRange) {
      final now = DateTime.now();
      setState(() {
        _startDate = DateTime(now.year, now.month, 1);
        _endDate = DateTime(now.year, now.month + 1, 0);
      });
    }
  }

  Future<void> _selectDateRange() async {
    final DateTimeRange? picked = await showDateRangePicker(
      context: context,
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
      initialDateRange: _startDate != null && _endDate != null
          ? DateTimeRange(start: _startDate!, end: _endDate!)
          : null,
      locale: const Locale('ar'),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: Theme.of(context).colorScheme.copyWith(
              primary: Theme.of(context).primaryColor,
            ),
          ),
          child: child!,
        );
      },
    );

    if (picked != null) {
      setState(() {
        _startDate = picked.start;
        _endDate = picked.end;
      });
    }
  }

  Future<void> _generateReport() async {
    if (_selectedCurrency == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('يرجى اختيار العملة'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    if (widget.reportType.requiresDateRange && (_startDate == null || _endDate == null)) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('يرجى تحديد الفترة الزمنية'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    if (widget.reportType.requiresCategory && _selectedCategory == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('يرجى اختيار الفئة'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    setState(() {
      _isGenerating = true;
    });

    try {
      final config = ReportConfig(
        type: widget.reportType,
        currency: _selectedCurrency,
        startDate: _startDate,
        endDate: _endDate,
        exportFormat: _selectedFormat,
        categoryId: _selectedCategory?.id.toString(),
      );

      final reportService = ReportService();
      final success = await reportService.generateAndExportReport(context, config);

      if (success && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم إنشاء التقرير بنجاح'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في إنشاء التقرير: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isGenerating = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.reportType.arabicName),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
      ),
      body: Consumer3<CurrencyProvider, CategoryProvider, PersonalInfoProvider>(
        builder: (context, currencyProvider, categoryProvider, personalInfoProvider, child) {
          if (currencyProvider.isLoading || categoryProvider.isLoading) {
            return const Center(
              child: CircularProgressIndicator(),
            );
          }

          return SingleChildScrollView(
            padding: const EdgeInsets.all(AppConstants.defaultPadding),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                // Report Info Card
                _buildReportInfoCard(),
                const SizedBox(height: AppConstants.defaultPadding),

                // Currency Selection
                _buildCurrencySelection(currencyProvider.currencies),
                const SizedBox(height: AppConstants.defaultPadding),

                // Category Selection (if required)
                if (widget.reportType.requiresCategory) ...[
                  _buildCategorySelection(categoryProvider.categories),
                  const SizedBox(height: AppConstants.defaultPadding),
                ],

                // Date Range Selection (if required)
                if (widget.reportType.requiresDateRange) ...[
                  _buildDateRangeSelection(),
                  const SizedBox(height: AppConstants.defaultPadding),
                ],

                // Export Format Selection
                _buildExportFormatSelection(),
                const SizedBox(height: AppConstants.defaultPadding * 2),

                // Generate Button
                ElevatedButton(
                  onPressed: _isGenerating ? null : _generateReport,
                  style: ElevatedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(AppConstants.defaultBorderRadius),
                    ),
                  ),
                  child: _isGenerating
                      ? const Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            SizedBox(
                              width: 20,
                              height: 20,
                              child: CircularProgressIndicator(strokeWidth: 2),
                            ),
                            SizedBox(width: 12),
                            Text('جاري إنشاء التقرير...'),
                          ],
                        )
                      : Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(_getFormatIcon(_selectedFormat)),
                            const SizedBox(width: 8),
                            Text(
                              'إنشاء التقرير (${_selectedFormat.arabicName})',
                              style: const TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildReportInfoCard() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppConstants.defaultBorderRadius),
      ),
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.info_outline,
                  color: Theme.of(context).primaryColor,
                ),
                const SizedBox(width: 8),
                Text(
                  'معلومات التقرير',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: AppConstants.smallPadding),
            Text(
              widget.reportType.description,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCurrencySelection(List<Currency> currencies) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppConstants.defaultBorderRadius),
      ),
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'العملة',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppConstants.smallPadding),
            DropdownButtonFormField<Currency>(
              value: _selectedCurrency,
              decoration: InputDecoration(
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(AppConstants.defaultBorderRadius),
                ),
                prefixIcon: const Icon(Icons.monetization_on),
              ),
              items: currencies.map((currency) {
                return DropdownMenuItem<Currency>(
                  value: currency,
                  child: Text('${currency.name} (${currency.symbol})'),
                );
              }).toList(),
              onChanged: (Currency? value) {
                setState(() {
                  _selectedCurrency = value;
                });
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCategorySelection(List<model.Category> categories) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppConstants.defaultBorderRadius),
      ),
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'الفئة',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppConstants.smallPadding),
            DropdownButtonFormField<model.Category>(
              value: _selectedCategory,
              decoration: InputDecoration(
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(AppConstants.defaultBorderRadius),
                ),
                prefixIcon: const Icon(Icons.category),
              ),
              items: categories.map((category) {
                return DropdownMenuItem<model.Category>(
                  value: category,
                  child: Text(category.name),
                );
              }).toList(),
              onChanged: (model.Category? value) {
                setState(() {
                  _selectedCategory = value;
                });
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDateRangeSelection() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppConstants.defaultBorderRadius),
      ),
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'الفترة الزمنية',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppConstants.smallPadding),
            InkWell(
              onTap: _selectDateRange,
              child: Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey[400]!),
                  borderRadius: BorderRadius.circular(AppConstants.defaultBorderRadius),
                ),
                child: Row(
                  children: [
                    const Icon(Icons.date_range),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Text(
                        _startDate != null && _endDate != null
                            ? 'من ${_formatDate(_startDate!)} إلى ${_formatDate(_endDate!)}'
                            : 'اختر الفترة الزمنية',
                        style: TextStyle(
                          color: _startDate != null && _endDate != null
                              ? Colors.black
                              : Colors.grey[600],
                        ),
                      ),
                    ),
                    Icon(
                      Icons.arrow_drop_down,
                      color: Colors.grey[600],
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildExportFormatSelection() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppConstants.defaultBorderRadius),
      ),
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'تنسيق التصدير',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppConstants.smallPadding),
            ...ExportFormat.values.map((format) {
              return RadioListTile<ExportFormat>(
                title: Row(
                  children: [
                    Icon(_getFormatIcon(format)),
                    const SizedBox(width: 8),
                    Text(format.arabicName),
                  ],
                ),
                value: format,
                groupValue: _selectedFormat,
                onChanged: (ExportFormat? value) {
                  if (value != null) {
                    setState(() {
                      _selectedFormat = value;
                    });
                  }
                },
              );
            }),
          ],
        ),
      ),
    );
  }

  IconData _getFormatIcon(ExportFormat format) {
    switch (format) {
      case ExportFormat.excel:
        return Icons.table_chart;
      case ExportFormat.pdf:
        return Icons.picture_as_pdf;
      case ExportFormat.whatsapp:
        return Icons.message;
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}
