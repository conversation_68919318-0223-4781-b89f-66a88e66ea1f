import 'package:flutter/services.dart';
import 'package:intl/intl.dart';
import '../models/currency.dart';
import 'constants.dart';

class CurrencyFormatter {
  static String formatAmount(double amount, Currency currency) {
    final formatter = NumberFormat.currency(
      symbol: currency.symbol,
      decimalDigits: AppConstants.currencyDecimalPlaces,
      locale: 'ar_SA',
    );
    return formatter.format(amount);
  }

  static String formatAmountWithoutSymbol(double amount) {
    final formatter = NumberFormat.decimalPattern('ar_SA');
    formatter.minimumFractionDigits = AppConstants.currencyDecimalPlaces;
    formatter.maximumFractionDigits = AppConstants.currencyDecimalPlaces;
    return formatter.format(amount);
  }

  static double parseAmount(String amountString) {
    // Remove any non-numeric characters except decimal point
    String cleanString = amountString.replaceAll(RegExp(r'[^\d.]'), '');
    return double.tryParse(cleanString) ?? 0.0;
  }
}

class PhoneNumberFormatter extends TextInputFormatter {
  @override
  TextEditingValue formatEditUpdate(
    TextEditingValue oldValue,
    TextEditingValue newValue,
  ) {
    // Remove any non-numeric characters except +
    String newText = newValue.text.replaceAll(RegExp(r'[^\d+]'), '');
    
    // Limit length
    if (newText.length > AppConstants.maxPhoneLength) {
      newText = newText.substring(0, AppConstants.maxPhoneLength);
    }

    // Format Saudi phone numbers
    if (newText.startsWith('05') || newText.startsWith('5')) {
      if (newText.startsWith('5')) {
        newText = '0$newText';
      }
      if (newText.length > 3) {
        newText = '${newText.substring(0, 3)} ${newText.substring(3)}';
      }
      if (newText.length > 8) {
        newText = '${newText.substring(0, 8)} ${newText.substring(8)}';
      }
    }

    return TextEditingValue(
      text: newText,
      selection: TextSelection.collapsed(offset: newText.length),
    );
  }
}

class AmountInputFormatter extends TextInputFormatter {
  @override
  TextEditingValue formatEditUpdate(
    TextEditingValue oldValue,
    TextEditingValue newValue,
  ) {
    // Allow only numbers and one decimal point
    String newText = newValue.text.replaceAll(RegExp(r'[^\d.]'), '');
    
    // Ensure only one decimal point
    List<String> parts = newText.split('.');
    if (parts.length > 2) {
      newText = '${parts[0]}.${parts.sublist(1).join('')}';
    }
    
    // Limit decimal places
    if (parts.length == 2 && parts[1].length > AppConstants.currencyDecimalPlaces) {
      parts[1] = parts[1].substring(0, AppConstants.currencyDecimalPlaces);
      newText = '${parts[0]}.${parts[1]}';
    }
    
    // Check maximum amount
    double? amount = double.tryParse(newText);
    if (amount != null && amount > AppConstants.maxAmount) {
      return oldValue;
    }

    return TextEditingValue(
      text: newText,
      selection: TextSelection.collapsed(offset: newText.length),
    );
  }
}

class DateFormatter {
  static String formatDate(DateTime date) {
    return DateFormat(AppConstants.arabicDateFormat, 'ar_SA').format(date);
  }

  static String formatDateTime(DateTime dateTime) {
    return DateFormat(AppConstants.dateTimeFormat, 'ar_SA').format(dateTime);
  }

  static DateTime? parseDate(String dateString) {
    try {
      return DateFormat(AppConstants.arabicDateFormat, 'ar_SA').parse(dateString);
    } catch (e) {
      try {
        return DateFormat(AppConstants.dateFormat).parse(dateString);
      } catch (e) {
        return null;
      }
    }
  }

  static String getRelativeDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays == 0) {
      return 'اليوم';
    } else if (difference.inDays == 1) {
      return 'أمس';
    } else if (difference.inDays < 7) {
      return 'منذ ${difference.inDays} أيام';
    } else if (difference.inDays < 30) {
      final weeks = (difference.inDays / 7).floor();
      return weeks == 1 ? 'منذ أسبوع' : 'منذ $weeks أسابيع';
    } else if (difference.inDays < 365) {
      final months = (difference.inDays / 30).floor();
      return months == 1 ? 'منذ شهر' : 'منذ $months أشهر';
    } else {
      final years = (difference.inDays / 365).floor();
      return years == 1 ? 'منذ سنة' : 'منذ $years سنوات';
    }
  }
}

class ArabicNumberFormatter {
  static const Map<String, String> _arabicDigits = {
    '0': '٠',
    '1': '١',
    '2': '٢',
    '3': '٣',
    '4': '٤',
    '5': '٥',
    '6': '٦',
    '7': '٧',
    '8': '٨',
    '9': '٩',
  };

  static const Map<String, String> _englishDigits = {
    '٠': '0',
    '١': '1',
    '٢': '2',
    '٣': '3',
    '٤': '4',
    '٥': '5',
    '٦': '6',
    '٧': '7',
    '٨': '8',
    '٩': '9',
  };

  static String toArabicDigits(String input) {
    String result = input;
    _arabicDigits.forEach((english, arabic) {
      result = result.replaceAll(english, arabic);
    });
    return result;
  }

  static String toEnglishDigits(String input) {
    String result = input;
    _englishDigits.forEach((arabic, english) {
      result = result.replaceAll(arabic, english);
    });
    return result;
  }
}

class TextDirectionHelper {
  static bool isRTL(String text) {
    // Check if text contains Arabic characters
    return RegExp(r'[\u0600-\u06FF]').hasMatch(text);
  }

  static TextDirection getTextDirection(String text) {
    return isRTL(text) ? TextDirection.rtl : TextDirection.ltr;
  }
}
