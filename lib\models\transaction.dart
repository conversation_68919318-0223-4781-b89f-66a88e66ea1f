enum TransactionType {
  paymentReceived, // دفعة مستلمة
  paymentMade, // دفعة مدفوعة
  adjustment, // تعديل
}

class Transaction {
  final int? id;
  final int accountId;
  final double amount; // Positive for received, negative for made payments
  final String? description;
  final DateTime date;
  final TransactionType type;
  final DateTime createdAt;
  final DateTime? updatedAt;

  Transaction({
    this.id,
    required this.accountId,
    required this.amount,
    this.description,
    required this.date,
    required this.type,
    DateTime? createdAt,
    this.updatedAt,
  }) : createdAt = createdAt ?? DateTime.now();

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'accountId': accountId,
      'amount': amount,
      'description': description,
      'date': date.millisecondsSinceEpoch,
      'type': type.index,
      'createdAt': createdAt.millisecondsSinceEpoch,
      'updatedAt': updatedAt?.millisecondsSinceEpoch,
    };
  }

  factory Transaction.fromMap(Map<String, dynamic> map) {
    return Transaction(
      id: map['id'],
      accountId: map['accountId'],
      amount: map['amount'].toDouble(),
      description: map['description'],
      date: DateTime.fromMillisecondsSinceEpoch(map['date']),
      type: TransactionType.values[map['type']],
      createdAt: DateTime.fromMillisecondsSinceEpoch(map['createdAt']),
      updatedAt: map['updatedAt'] != null
          ? DateTime.fromMillisecondsSinceEpoch(map['updatedAt'])
          : null,
    );
  }

  Transaction copyWith({
    int? id,
    int? accountId,
    double? amount,
    String? description,
    DateTime? date,
    TransactionType? type,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Transaction(
      id: id ?? this.id,
      accountId: accountId ?? this.accountId,
      amount: amount ?? this.amount,
      description: description ?? this.description,
      date: date ?? this.date,
      type: type ?? this.type,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? DateTime.now(),
    );
  }

  String get typeArabic {
    switch (type) {
      case TransactionType.paymentReceived:
        return 'دفعة مستلمة';
      case TransactionType.paymentMade:
        return 'دفعة مدفوعة';
      case TransactionType.adjustment:
        return 'تعديل';
    }
  }

  bool get isPositive => amount >= 0;

  @override
  String toString() {
    return 'Transaction{id: $id, accountId: $accountId, amount: $amount, type: $type, date: $date}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Transaction &&
        other.id == id &&
        other.accountId == accountId &&
        other.amount == amount &&
        other.description == description &&
        other.date == date &&
        other.type == type;
  }

  @override
  int get hashCode {
    return id.hashCode ^
        accountId.hashCode ^
        amount.hashCode ^
        description.hashCode ^
        date.hashCode ^
        type.hashCode;
  }
}
