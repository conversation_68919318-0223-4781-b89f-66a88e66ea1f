class Currency {
  final int? id;
  final String name;
  final String symbol;
  final String code;
  final bool isDefault;
  final DateTime createdAt;

  Currency({
    this.id,
    required this.name,
    required this.symbol,
    required this.code,
    this.isDefault = false,
    DateTime? createdAt,
  }) : createdAt = createdAt ?? DateTime.now();

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'symbol': symbol,
      'code': code,
      'isDefault': isDefault ? 1 : 0,
      'createdAt': createdAt.millisecondsSinceEpoch,
    };
  }

  factory Currency.fromMap(Map<String, dynamic> map) {
    return Currency(
      id: map['id'],
      name: map['name'],
      symbol: map['symbol'],
      code: map['code'],
      isDefault: map['isDefault'] == 1,
      createdAt: DateTime.fromMillisecondsSinceEpoch(map['createdAt']),
    );
  }

  Currency copyWith({
    int? id,
    String? name,
    String? symbol,
    String? code,
    bool? isDefault,
    DateTime? createdAt,
  }) {
    return Currency(
      id: id ?? this.id,
      name: name ?? this.name,
      symbol: symbol ?? this.symbol,
      code: code ?? this.code,
      isDefault: isDefault ?? this.isDefault,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  @override
  String toString() {
    return 'Currency{id: $id, name: $name, symbol: $symbol, code: $code, isDefault: $isDefault}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Currency &&
        other.id == id &&
        other.name == name &&
        other.symbol == symbol &&
        other.code == code &&
        other.isDefault == isDefault;
  }

  @override
  int get hashCode {
    return id.hashCode ^
        name.hashCode ^
        symbol.hashCode ^
        code.hashCode ^
        isDefault.hashCode;
  }
}
